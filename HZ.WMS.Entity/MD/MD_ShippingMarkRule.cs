using SqlSugar;
using System;
using System.ComponentModel;

namespace HZ.WMS.Entity.MD
{
    /// <summary>
    /// 唛头规则
    /// </summary>
    [SugarTable("MD_ShippingMarkRule")]
    public class MD_ShippingMarkRule : BaseEntity
    {
        /// <summary>
        /// ID
        /// </summary>
        [SugarColumn(IsPrimaryKey = true)]
        [Description("ID")]
        public string Id { get; set; }

        /// <summary>
        /// 发货单位
        /// </summary>
        [Description("发货单位")]
        public string DeliveryCompany { get; set; }

        /// <summary>
        /// 唛头打印方式
        /// </summary>
        [Description("唛头打印方式")]
        public string ShippingMarkPrintMethod { get; set; }

        /// <summary>
        /// 唛头封装方式
        /// </summary>
        [Description("唛头封装方式")]
        public string ShippingMarkEncapsulationMethod { get; set; }

        /// <summary>
        /// 唛头纸
        /// </summary>
        [Description("唛头纸")]
        public string ShippingMarkPaper { get; set; }

        /// <summary>
        /// 唛头纸选项
        /// </summary>
        [Description("唛头纸选项")]
        public string ShippingMarkPaperOption { get; set; }

        /// <summary>
        /// 唛头纸尺寸
        /// </summary>
        [Description("唛头纸尺寸")]
        public string ShippingMarkPaperSize { get; set; }

        /// <summary>
        /// 唛头打印系统
        /// </summary>
        [Description("唛头打印系统")]
        public string ShippingMarkPrintSystem { get; set; }

        /// <summary>
        /// 唛头封装方式
        /// </summary>
        [Description("唛头封装方式")]
        public string ShippingMarkPackagingMethod { get; set; }

        /// <summary>
        /// 是否到站/港
        /// </summary>
        [Description("是否到站/港")]
        public string IsArrivalStationOrPort { get; set; }

        /// <summary>
        /// 打印模板ID
        /// </summary>
        [Description("打印模板ID")]
        public string PrintTemplateId { get; set; }

        /// <summary>
        /// 刷字
        /// </summary>
        [Description("刷字")]
        public string Brush { get; set; }

        /// <summary>
        /// 是否出口
        /// </summary>
        [Description("是否出口")]
        public string IsExport { get; set; }

        /// <summary>
        /// 打印方向
        /// </summary>
        [Description("打印方向")]
        public string PrintDirection { get; set; }

        /// <summary>
        /// 参数校验
        /// </summary>
        [Description("参数校验")]
        public string ParameterCheckStr { get; set; }
    }
}
