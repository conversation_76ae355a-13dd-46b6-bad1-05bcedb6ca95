using System.ComponentModel;

namespace HZ.WMS.Entity.MD.Import
{
    /// <summary>
    /// 唛头规则导入
    /// </summary>
    public class MD_ShippingMarkRuleImport
    {
        /// <summary>
        /// 发货单位
        /// </summary>
        [Description("发货单位")]
        public string DeliveryCompany { get; set; }

        /// <summary>
        /// 唛头打印方式
        /// </summary>
        [Description("唛头打印方式")]
        public string ShippingMarkPrintMethod { get; set; }

        /// <summary>
        /// 唛头封装方式
        /// </summary>
        [Description("唛头封装方式")]
        public string ShippingMarkEncapsulationMethod { get; set; }

        /// <summary>
        /// 唛头纸
        /// </summary>
        [Description("唛头纸")]
        public string ShippingMarkPaper { get; set; }

        /// <summary>
        /// 唛头纸选项
        /// </summary>
        [Description("唛头纸选项")]
        public string ShippingMarkPaperOption { get; set; }
        
        /// <summary>
        /// 唛头纸尺寸
        /// </summary>
        [Description("唛头纸尺寸")]
        public string ShippingMarkPaperSize { get; set; }

        /// <summary>
        /// 模板名称
        /// </summary>
        [Description("模板名称")]
        public string TemplateName { get; set; }

        /// <summary>
        /// 刷字
        /// </summary>
        [Description("刷字")]
        public string Brush { get; set; }

        /// <summary>
        /// 是否出口
        /// </summary>
        [Description("是否出口")]
        public string IsExport { get; set; }

        /// <summary>
        /// 打印方向
        /// </summary>
        [Description("打印方向")]
        public string PrintDirection { get; set; }

        /// <summary>
        /// 备注
        /// </summary>
        [Description("备注")]
        public string Remark { get; set; }
        
        /// <summary>
        /// 参数校验
        /// </summary>
        [Description("参数校验")]
        public string ParameterCheckStr { get; set; }
    }
}
