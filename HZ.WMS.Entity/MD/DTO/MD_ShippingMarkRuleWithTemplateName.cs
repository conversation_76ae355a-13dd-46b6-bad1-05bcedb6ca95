using System;
using System.ComponentModel;

namespace HZ.WMS.Entity.MD.DTO
{
    /// <summary>
    /// 唛头规则包含模板名称
    /// </summary>
    public class MD_ShippingMarkRuleWithTemplateName
    {
        /// <summary>
        /// ID
        /// </summary>
        [Description("ID")]
        public string Id { get; set; }

        /// <summary>
        /// 发货单位
        /// </summary>
        [Description("发货单位")]
        public string DeliveryCompany { get; set; }

        /// <summary>
        /// 唛头打印方式
        /// </summary>
        [Description("唛头打印方式")]
        public string ShippingMarkPrintMethod { get; set; }

        /// <summary>
        /// 唛头封装方式
        /// </summary>
        [Description("唛头封装方式")]
        public string ShippingMarkEncapsulationMethod { get; set; }

        /// <summary>
        /// 唛头纸
        /// </summary>
        [Description("唛头纸")]
        public string ShippingMarkPaper { get; set; }

        /// <summary>
        /// 唛头纸选项
        /// </summary>
        [Description("唛头纸选项")]
        public string ShippingMarkPaperOption { get; set; }

        /// <summary>
        /// 唛头纸尺寸
        /// </summary>
        [Description("唛头纸尺寸")]
        public string ShippingMarkPaperSize { get; set; }

        /// <summary>
        /// 唛头打印系统
        /// </summary>
        [Description("唛头打印系统")]
        public string ShippingMarkPrintSystem { get; set; }

        /// <summary>
        /// 唛头封装方式
        /// </summary>
        [Description("唛头封装方式")]
        public string ShippingMarkPackagingMethod { get; set; }

        /// <summary>
        /// 是否到站/港
        /// </summary>
        [Description("是否到站/港")]
        public string IsArrivalStationOrPort { get; set; }

        /// <summary>
        /// 打印模板ID
        /// </summary>
        [Description("打印模板ID")]
        public string PrintTemplateId { get; set; }

        /// <summary>
        /// 模板名称
        /// </summary>
        [Description("模板名称")]
        public string TemplateName { get; set; }

        /// <summary>
        /// 刷字
        /// </summary>
        [Description("刷字")]
        public string Brush { get; set; }

        /// <summary>
        /// 是否出口
        /// </summary>
        [Description("是否出口")]
        public string IsExport { get; set; }

        /// <summary>
        /// 打印方向
        /// </summary>
        [Description("打印方向")]
        public string PrintDirection { get; set; }

        /// <summary>
        /// 备注
        /// </summary>
        [Description("备注")]
        public string Remark { get; set; }

        /// <summary>
        /// 是否删除
        /// </summary>
        [Description("是否删除")]
        public bool IsDelete { get; set; }

        /// <summary>
        /// 创建用户
        /// </summary>
        [Description("创建用户")]
        public string CUser { get; set; }

        /// <summary>
        /// 创建时间
        /// </summary>
        [Description("创建时间")]
        public DateTime? CTime { get; set; }

        /// <summary>
        /// 修改用户
        /// </summary>
        [Description("修改用户")]
        public string MUser { get; set; }

        /// <summary>
        /// 修改时间
        /// </summary>
        [Description("修改时间")]
        public DateTime? MTime { get; set; }

        /// <summary>
        /// 删除用户
        /// </summary>
        [Description("删除用户")]
        public string DUser { get; set; }

        /// <summary>
        /// 删除时间
        /// </summary>
        [Description("删除时间")]
        public DateTime? DTime { get; set; }
        
        /// <summary>
        /// 参数校验
        /// </summary>
        [Description("参数校验")]
        public string ParameterCheck { get; set; }
    }
}
