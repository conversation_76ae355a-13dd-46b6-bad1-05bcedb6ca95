<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="15.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <Import Project="$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props" Condition="Exists('$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props')" />
  <PropertyGroup>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">AnyCPU</Platform>
    <ProjectGuid>{A56C0618-C820-42E7-89B5-E5DA01C72729}</ProjectGuid>
    <OutputType>Library</OutputType>
    <AppDesignerFolder>Properties</AppDesignerFolder>
    <RootNamespace>HZ.WMS.Entity</RootNamespace>
    <AssemblyName>HZ.WMS.Entity</AssemblyName>
    <TargetFrameworkVersion>v4.6.2</TargetFrameworkVersion>
    <FileAlignment>512</FileAlignment>
    <Deterministic>true</Deterministic>
    <TargetFrameworkProfile />
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|AnyCPU' ">
    <DebugSymbols>true</DebugSymbols>
    <DebugType>full</DebugType>
    <Optimize>false</Optimize>
    <OutputPath>bin\Debug\</OutputPath>
    <DefineConstants>DEBUG;TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
    <DocumentationFile>bin\Debug\HZ.WMS.Entity.xml</DocumentationFile>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|AnyCPU' ">
    <DebugType>pdbonly</DebugType>
    <Optimize>true</Optimize>
    <OutputPath>bin\Release\</OutputPath>
    <DefineConstants>TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
  </PropertyGroup>
  <ItemGroup>
    <Reference Include="ICSharpCode.SharpZipLib, Version=********, Culture=neutral, PublicKeyToken=1b03e6acf1164f73, processorArchitecture=MSIL">
      <HintPath>..\packages\SharpZipLib.1.3.3\lib\net45\ICSharpCode.SharpZipLib.dll</HintPath>
    </Reference>
    <Reference Include="NPOI, Version=*******, Culture=neutral, PublicKeyToken=0df73ec7942b34e1, processorArchitecture=MSIL">
      <HintPath>..\packages\NPOI.2.5.6\lib\net45\NPOI.dll</HintPath>
    </Reference>
    <Reference Include="NPOI.OOXML, Version=*******, Culture=neutral, PublicKeyToken=0df73ec7942b34e1, processorArchitecture=MSIL">
      <HintPath>..\packages\NPOI.2.5.6\lib\net45\NPOI.OOXML.dll</HintPath>
    </Reference>
    <Reference Include="NPOI.OpenXml4Net, Version=*******, Culture=neutral, PublicKeyToken=0df73ec7942b34e1, processorArchitecture=MSIL">
      <HintPath>..\packages\NPOI.2.5.6\lib\net45\NPOI.OpenXml4Net.dll</HintPath>
    </Reference>
    <Reference Include="NPOI.OpenXmlFormats, Version=*******, Culture=neutral, PublicKeyToken=0df73ec7942b34e1, processorArchitecture=MSIL">
      <HintPath>..\packages\NPOI.2.5.6\lib\net45\NPOI.OpenXmlFormats.dll</HintPath>
    </Reference>
    <Reference Include="SqlSugar, Version=*********, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\packages\SqlSugar.*********\lib\SqlSugar.dll</HintPath>
    </Reference>
    <Reference Include="System" />
    <Reference Include="System.ComponentModel.DataAnnotations" />
    <Reference Include="System.Core" />
    <Reference Include="System.Runtime.Serialization" />
    <Reference Include="System.Security" />
    <Reference Include="System.Xml.Linq" />
    <Reference Include="System.Data.DataSetExtensions" />
    <Reference Include="Microsoft.CSharp" />
    <Reference Include="System.Data" />
    <Reference Include="System.Net.Http" />
    <Reference Include="System.Xml" />
  </ItemGroup>
  <ItemGroup>
    <Compile Include="Base\BaseEntity.cs" />
    <Compile Include="Base\BinLocationEntity.cs" />
    <Compile Include="Base\EntityComparer.cs" />
    <Compile Include="CableBasic\CustomerPart.cs" />
    <Compile Include="CableBasic\CustomerPartParams.cs" />
    <Compile Include="CableBasic\ProductionNoRule.cs" />
    <Compile Include="CablePartProduce\req\Cable_PartWorkOrderListReq.cs" />
    <Compile Include="CablePartProduce\SD_Cable_Sale_PartOrderDetails.cs" />
    <Compile Include="CablePartProduce\SD_Cable_Sale_PartOrderInfo.cs" />
    <Compile Include="CableProduce\Cable_ProductionOrder.cs" />
    <Compile Include="CableProduce\Cable_WorkOrder.cs" />
    <Compile Include="CableProduce\req\Cable_PrintUpdate.cs" />
    <Compile Include="CableProduce\req\Cable_ProductionOrderListReq.cs" />
    <Compile Include="CableProduce\req\Cable_ProductionOrderReq.cs" />
    <Compile Include="CableProduce\req\Cable_WorkOrderConfirmScanReq.cs" />
    <Compile Include="CableProduce\req\Cable_WorkOrderInfoReq.cs" />
    <Compile Include="CableProduce\req\Cable_WorkOrderListReq.cs" />
    <Compile Include="CableProduce\res\Cable_ProductionOrderListRes.cs" />
    <Compile Include="CableProduce\SD_Cable_OrderSerialNum.cs" />
    <Compile Include="CableProduce\SD_Cable_Sale_OrderDetails.cs" />
    <Compile Include="CableProduce\SD_Cable_Sale_OrderFloor.cs" />
    <Compile Include="CableProduce\SD_Cable_Sale_OrderInfo.cs" />
    <Compile Include="CableProduce\SD_Cable_Sale_Parameter.cs" />
    <Compile Include="CableSale\Delivery.cs" />
    <Compile Include="CableSale\req\Cable_DeliveryListReq.cs" />
    <Compile Include="CableSale\req\Cable_ShippingPlanListReq.cs" />
    <Compile Include="CableSale\req\SD_Cable_Sale_OrderInfoListReq.cs" />
    <Compile Include="CableSale\SD_Cable_Sale_OrderExport.cs" />
    <Compile Include="CableSale\ShippingPlan.cs" />
    <Compile Include="EAP\SGEAP_CUS_Order.cs" />
    <Compile Include="MD\CC_SafepartCustomer.cs" />
    <Compile Include="MD\CC_SafepartRule.cs" />
    <Compile Include="MD\DTO\MD_ShippingMarkRuleWithTemplateName.cs" />
    <Compile Include="MD\Import\MD_BasicSpecificationImport.cs" />
    <Compile Include="MD\Import\MD_NonStandardConfigImport.cs" />

    <Compile Include="MD\Import\MD_PrintTemplateImport.cs" />
    <Compile Include="MD\Import\MD_PrintTemplateParameterImport.cs" />
    <Compile Include="MD\Import\MD_ReportStationImport.cs" />
    <Compile Include="MD\Import\MD_ReportStationMaterialImport.cs" />
    <Compile Include="MD\Import\MD_ShippingMarkRuleImport.cs" />
    <Compile Include="MD\MD_BasicSpecification.cs" />
    <Compile Include="MD\MD_Customer.cs" />
    <Compile Include="MD\MD_CustomerAdd.cs" />
    <Compile Include="MD\MD_CustomerDistanceRateA.cs" />
    <Compile Include="MD\MD_CustomerModelDeliveryAdvance.cs" />
    <Compile Include="MD\MD_CustomerProduce.cs" />
    <Compile Include="MD\MD_CustomerWeightPriceB.cs" />
    <Compile Include="MD\MD_CustomerWeightRateA.cs" />
    <Compile Include="MD\MD_CustomerWeightRateC.cs" />
    <Compile Include="MD\MD_FreightMileage.cs" />
    <Compile Include="MD\MD_LineBatch.cs" />
    <Compile Include="MD\MD_NonStandardConfig.cs" />

    <Compile Include="MD\MD_PartCode.cs" />
    <Compile Include="MD\MD_PartMakeCompany.cs" />
    <Compile Include="MD\MD_PartProduceLine.cs" />
    <Compile Include="MD\MD_PrintTemplate.cs" />
    <Compile Include="MD\MD_PrintTemplateParameter.cs" />
    <Compile Include="MD\DTO\MD_PrintTemplateWithParameterName.cs" />
    <Compile Include="MD\MD_ProcessInspection.cs" />
    <Compile Include="MD\MD_ProduceLine.cs" />
    <Compile Include="MD\MD_ProduceLineCapacity.cs" />
    <Compile Include="MD\MD_ProductCategory.cs" />
    <Compile Include="MD\MD_ReportStation.cs" />
    <Compile Include="MD\MD_ReportStationMaterial.cs" />
    <Compile Include="MD\MD_ReportStationUserRelation.cs" />
    <Compile Include="MD\MD_ShippingMarkRule.cs" />
    <Compile Include="MD\Req\MD_BasicSpecificationListReq.cs" />
    <Compile Include="MD\Req\MD_NonStandardConfigListReq.cs" />

    <Compile Include="MD\Req\MD_PrintTemplateListReq.cs" />
    <Compile Include="MD\Req\MD_PrintTemplateParameterListReq.cs" />
    <Compile Include="MD\Req\MD_ProduceLineCapacityListReq.cs" />
    <Compile Include="MD\MD_ProductionDistributionSetting.cs" />
    <Compile Include="MD\MD_ProductionDistributionSettingImport.cs" />
    <Compile Include="MD\MD_Province_City_District.cs" />
    <Compile Include="MD\MD_Supplier.cs" />
    <Compile Include="MD\MD_UnitCodeConversion.cs" />
    <Compile Include="MD\PRT_PurchaseDelivery.cs" />
    <Compile Include="MD\Req\MD_CustomerModelDeliveryAdvanceListReq.cs" />
    <Compile Include="MD\Req\MD_CustomerProduceListReq.cs" />
    <Compile Include="MD\Req\MD_LineBatchListReq.cs" />
    <Compile Include="MD\Req\MD_PartMakeCompanyListReq.cs" />
    <Compile Include="MD\Req\MD_PartProduceLineListReq.cs" />
    <Compile Include="MD\Req\MD_ProduceLineCapacityListReq.cs" />
    <Compile Include="MD\Req\MD_ProduceLineListReq.cs" />
    <Compile Include="MD\Import\MD_ProduceLineImport.cs" />
    <Compile Include="MD\Import\MD_ProduceLineCapacityImport.cs" />
    <Compile Include="MD\Import\MD_CustomerModelDeliveryAdvanceImport.cs" />
    <Compile Include="MD\Import\MD_CustomerProduceImport.cs" />
    <Compile Include="MD\Import\MD_PartMakeCompanyImport.cs" />
    <Compile Include="MD\Import\MD_PartProduceLineImport.cs" />
    <Compile Include="MD\Import\MD_WorkCenterStationImport.cs" />
    <Compile Include="MD\MD_WorkCenterStation.cs" />
    <Compile Include="MD\Req\MD_ReportStationAddReq.cs" />
    <Compile Include="MD\Req\MD_ReportStationUpdateReq.cs" />
    <Compile Include="MD\Req\MD_WorkCenterStationListReq.cs" />
    <Compile Include="MD\RPT_StockAge.cs" />
    <Compile Include="MD\ViewModel\MD_FreightMileage_View.cs" />
    <Compile Include="MD\ViewModel\MD_StockMain_View.cs" />
    <Compile Include="MD\ViewModel\SAP_ItemBinLocation_View.cs" />
    <Compile Include="MM\Import\MM_BarCodeIm.cs" />
    <Compile Include="MM\Import\MM_BarCodeImport.cs" />
    <Compile Include="MM\Import\MM_RedeployApplyImport.cs" />
    <Compile Include="MM\Import\MM_TakeStockScanImport.cs" />
    <Compile Include="MM\MM_BarCode.cs" />
    <Compile Include="MM\MM_BarCodeScan.cs" />
    <Compile Include="MM\MM_DepReqDetailed.cs" />
    <Compile Include="MM\MM_DepRequisition.cs" />
    <Compile Include="MM\MM_EquipmentPicking.cs" />
    <Compile Include="MM\MM_Dispatch.cs" />
    <Compile Include="MM\MM_LendingOrder.cs" />
    <Compile Include="MM\MM_LendingOrderDetail.cs" />
    <Compile Include="MM\MM_LendingOrder_Return.cs" />
    <Compile Include="MM\MM_OtherIn.cs" />
    <Compile Include="MM\MM_OtherInDetail.cs" />
    <Compile Include="MM\MM_PickingApply.cs" />
    <Compile Include="MM\MM_PickingApplyDetail.cs" />
    <Compile Include="MM\MM_RedeployApply.cs" />
    <Compile Include="MM\MM_RedeployApplyDetail.cs" />
    <Compile Include="MM\MM_ScrapApplication.cs" />
    <Compile Include="MM\MM_ScrapApplicationDetail.cs" />
    <Compile Include="MM\MM_StockTodoPicking.cs" />
    <Compile Include="MM\MM_SupplierRepair.cs" />
    <Compile Include="MM\Parameters\MM_DepRequisitionParameters.cs" />
    <Compile Include="MM\Parameters\MM_DispatchParameters.cs" />
    <Compile Include="MM\Parameters\MM_EquipmentPickingParameters.cs" />
    <Compile Include="MM\Parameters\MM_LendingOrderParameters.cs" />
    <Compile Include="MM\Parameters\MM_MagnetsWarehousingParameters.cs" />
    <Compile Include="MM\Parameters\MM_OtherInParameters.cs" />
    <Compile Include="MM\Parameters\MM_PickingApplyParameters.cs" />
    <Compile Include="MM\Parameters\MM_RedeployApplyParameters.cs" />
    <Compile Include="MM\Parameters\MM_ReturnParameters.cs" />
    <Compile Include="MM\Parameters\MM_ScrapApplicationParameters.cs" />
    <Compile Include="MM\Parameters\MM_WarehousingDto.cs" />
    <Compile Include="MM\Parameters\MM_WarehousingParameters.cs" />
    <Compile Include="MM\MM_StockTodo.cs" />
    <Compile Include="MM\MM_StockTodoDetail.cs" />
    <Compile Include="MM\ViewModel\MM_OtherInExport_View.cs" />
    <Compile Include="MM\ViewModel\MM_RedeployApplyExport_View.cs" />
    <Compile Include="MM\ViewModel\MM_DepRequisitionExport_View.cs" />
    <Compile Include="MM\ViewModel\MM_LendingOrderExport_View.cs" />
    <Compile Include="MM\ViewModel\MD_StockForEquipmentPicking_View.cs" />
    <Compile Include="MM\MM_Return.cs" />
    <Compile Include="MM\MM_ReturnDetail.cs" />
    <Compile Include="MM\MM_WarehousingDetail.cs" />
    <Compile Include="MM\MM_MagnetsWarehousing.cs" />
    <Compile Include="MM\MM_Warehousing.cs" />
    <Compile Include="MM\ViewModel\MM_DepReqDetailed_View.cs" />
    <Compile Include="MM\ViewModel\MM_DispatchForPickingApply_View.cs" />
    <Compile Include="MM\ViewModel\MM_LendingOrderItem_View.cs" />
    <Compile Include="MM\ViewModel\MM_LendingOrderReturnItem_View.cs" />
    <Compile Include="MM\ViewModel\MM_PickingApplyDetailForDispatch_View.cs" />
    <Compile Include="MM\ViewModel\MM_ScrapApplicationExport_View.cs" />
    <Compile Include="MM\ViewModel\MM_SupplierRepair_View.cs" />
    <Compile Include="MM\ViewModel\MM_WarehousingExport_View.cs" />
    <Compile Include="MM\ViewModel\MM_WarehousingForRESBM_View.cs" />
    <Compile Include="MM\ViewModel\MM_WarehousingForSRMInspection_View.cs" />
    <Compile Include="MM\ViewModel\MM_PickingApplyForEKKO_View.cs" />
    <Compile Include="MM\ViewModel\MM_PickingApplyForMARC_View.cs" />
    <Compile Include="MM\ViewModel\MM_TakeStockPlan_View.cs" />
    <Compile Include="PO\Parameters\PO_PurchaseReceiptParameters.cs" />
    <Compile Include="PO\Parameters\PO_ReturnScanParameters.cs" />
    <Compile Include="PO\PO_PurchaseReceipt.cs" />
    <Compile Include="PO\PO_ReturnScanDetailed.cs" />
    <Compile Include="MD\ViewModel\RPT_StockDiff.cs" />
    <Compile Include="MM\ViewModel\RPT_StockAdjustHistory.cs" />
    <Compile Include="PO\ViewModel\PO_ReturnScanForEKKO_View.cs" />
    <Compile Include="PP\Dto\PP_PackSorting_Dto.cs" />
    <Compile Include="PP\Dto\PP_ProcessInspection_Dto.cs" />
    <Compile Include="PP\Dto\PP_ReturnMaterial_Dto.cs" />
    <Compile Include="PP\Dto\PP_SerialNoRelation_Dto.cs" />
    <Compile Include="PP\Dto\PP_ShippingMarkPrintRecordWithDetails.cs" />
    <Compile Include="PP\Dto\PP_ShippingMarkWithTemplateName.cs" />
    <Compile Include="PP\Import\PP_ShippingMarkImport.cs" />
    <Compile Include="PP\PP_MaterialDistribution.cs" />
    <Compile Include="PP\PP_MaterialDistributionDetail.cs" />
    <Compile Include="PP\PP_OverReceive.cs" />
    <Compile Include="PP\PP_OverReceiveDetail.cs" />
    <Compile Include="PP\PP_PackSorting.cs" />
    <Compile Include="PP\PP_PackSortingDetail.cs" />
    <Compile Include="PP\PP_ProcessInspection.cs" />
    <Compile Include="PP\PP_ProcessInspectionDetail.cs" />
    <Compile Include="PP\PP_ProductionFeedingDetail.cs" />
    <Compile Include="PP\PP_ProductionOrder.cs" />
    <Compile Include="MD\MD_BinLimit.cs" />
    <Compile Include="MD\MD_BinLocation.cs" />
    <Compile Include="MD\MD_Item.cs" />
    <Compile Include="MD\MD_LabelTemplete.cs" />
    <Compile Include="MD\MD_Region.cs" />
    <Compile Include="MD\MD_Stock.cs" />
    <Compile Include="MD\MD_Warehouse.cs" />
    <Compile Include="MD\MD_FixedNumDef.cs" />
    <Compile Include="MD\ViewModel\MD_Item_View.cs" />
    <Compile Include="MD\ViewModel\MD_StockView.cs" />
    <Compile Include="MM\MM_TakeStockPlan.cs" />
    <Compile Include="MM\MM_TakeStockPlanDetailed.cs" />
    <Compile Include="MM\MM_TakeStockScan.cs" />
    <Compile Include="MM\MM_FixedNumDef.cs" />
    <Compile Include="MM\ViewModel\MM_TakeStockPlanDetailedView.cs" />
    <Compile Include="PO\PO_ReturnScan.cs" />
    <Compile Include="PP\PP_ProductionFeeding.cs" />
    <Compile Include="PP\PP_ProductionReport.cs" />
    <Compile Include="PP\PP_ReturnMaterial.cs" />
    <Compile Include="PP\PP_ReturnMaterialDetail.cs" />
    <Compile Include="PP\Dto\PP_MaterialDistribution_Dto.cs" />
    <Compile Include="PP\PP_SerialNoRelation.cs" />
    <Compile Include="PP\PP_SerialNoRelationDetail.cs" />
    <Compile Include="PP\PP_ShippingMark.cs" />
    <Compile Include="PP\PP_ShippingMarkPrintRecord.cs" />
    <Compile Include="PP\Req\PP_ShippingMarkBatchUpdatePrintStatusReq.cs" />
    <Compile Include="PP\Req\PP_ShippingMarkListReq.cs" />
    <Compile Include="PP\Req\PP_ShippingMarkPrintRecordListReq.cs" />
    <Compile Include="PP\ViewModel\PP_AssignSerialNo_View.cs" />
    <Compile Include="PP\ViewModel\PP_ExportAssignSerialNo_View.cs" />
    <Compile Include="PP\ViewModel\PP_ExportDTLine_View.cs" />
    <Compile Include="PP\ViewModel\PP_ExportHostLine_View.cs" />
    <Compile Include="PP\ViewModel\PP_ExportHostScheduling_View.cs" />
    <Compile Include="PP\ViewModel\PP_ExportRepairLine_View.cs" />
    <Compile Include="PP\ViewModel\PP_FeedingDetail_View.cs" />
    <Compile Include="PP\ViewModel\PP_OverReceiveDetail_View.cs" />
    <Compile Include="PP\Dto\PP_OverReceive_Dto.cs" />
    <Compile Include="PP\Dto\PP_ProductionFeeding_Dto.cs" />
    <Compile Include="PP\ViewModel\PP_DistributionDetail_View.cs" />
    <Compile Include="PP\ViewModel\PP_ProcessInspection_View.cs" />
    <Compile Include="PP\ViewModel\PP_ProductionOrderDetail_View.cs" />
    <Compile Include="PP\ViewModel\PP_ProductionOrder_View.cs" />
    <Compile Include="PP\ViewModel\PP_ProductionReportExport_View.cs" />
    <Compile Include="PP\ViewModel\PP_ProductionReport_View.cs" />
    <Compile Include="PP\ViewModel\PP_ReturnMaterial_View.cs" />
    <Compile Include="Produce\AufnrComp.cs" />
    <Compile Include="Produce\DeliveryListItem.cs" />
    <Compile Include="Produce\DlvAufnr.cs" />
    <Compile Include="Produce\EditAufnr.cs" />
    <Compile Include="Produce\Produce_Report.cs" />
    <Compile Include="Produce\Produce_ReportDetail.cs" />
    <Compile Include="Produce\Produce_Scheduling.cs" />
    <Compile Include="Produce\Produce_SchedulingDetail.cs" />
    <Compile Include="Produce\Produce_PlanOrder.cs" />
    <Compile Include="Produce\Produce_PurchaseOrder.cs" />
    <Compile Include="Produce\Req\CompletePreProduceReq.cs" />
    <Compile Include="Produce\Req\CreatePlanOrdeReq.cs" />
    <Compile Include="Produce\Req\CreateProdOrderReq.cs" />
    <Compile Include="Produce\Req\CreatePurchaseOrderReq.cs" />
    <Compile Include="Produce\Req\CreatePurchaseReq.cs" />
    <Compile Include="Produce\Req\GenerationProduceSortReq.cs" />
    <Compile Include="Produce\Req\GetMatnrVersionReq.cs" />
    <Compile Include="Produce\Req\PcsStatusUpdateReq.cs" />
    <Compile Include="Produce\Req\Produce_ReportListReq.cs" />
    <Compile Include="Produce\Req\Produce_ReportScanReq.cs" />
    <Compile Include="Produce\Req\Produce_StationScanReq.cs" />
    <Compile Include="Produce\Req\Produce_VersionListReq.cs" />
    <Compile Include="Produce\Req\Produce_SchedulingDetailListReq.cs" />
    <Compile Include="Produce\Req\Produce_PlanOrderListReq.cs" />
    <Compile Include="Produce\Req\Produce_PurchaseOrderListReq.cs" />
    <Compile Include="Produce\Req\Produce_LineQueryReq.cs" />
    <Compile Include="Produce\Req\Produce_SchedulingPcsReq.cs" />
    <Compile Include="Produce\Req\Produce_SchedulingListReq.cs" />
    <Compile Include="Produce\Req\SaleBomReq.cs" />
    <Compile Include="Produce\Req\SetProduceSchedulingDateReq.cs" />
    <Compile Include="Produce\Req\SetProduceLineReq.cs" />
    <Compile Include="Produce\Req\SetSortAndBatchReq.cs" />
    <Compile Include="Produce\Res\CreatePlanRes.cs" />
    <Compile Include="Produce\Res\CreateProdOrderRes.cs" />
    <Compile Include="Produce\Res\CreateProdRes.cs" />
    <Compile Include="Produce\Res\CreatePruchase0rderRes.cs" />
    <Compile Include="Produce\Res\CreatePruchaseRes.cs" />
    <Compile Include="Produce\Res\GetMatnrVersionRes.cs" />
    <Compile Include="Produce\Res\Produce_ReportImportTemplateRes.cs" />
    <Compile Include="Produce\Res\Produce_UserStationRes.cs" />
    <Compile Include="Produce\Res\SaleBomRes.cs" />
    <Compile Include="Produce\Res\WorkProcessRes.cs" />
    <Compile Include="Properties\AssemblyInfo.cs" />
    <Compile Include="QM\Parameters\QM_InspectionDto.cs" />
    <Compile Include="QM\Parameters\QM_PurchaseInspectionParameters.cs" />
    <Compile Include="QM\QM_PurchaseInspection.cs" />
    <Compile Include="QM\ViewModel\QM_InspectionForReceipt_View.cs" />
    <Compile Include="RPT\RPT_ItemMoveView.cs" />
    <Compile Include="RPT\ViewModel\V_NotPostStockMove.cs" />
    <Compile Include="SAP\Req\CheckMatnrPrice.cs" />
    <Compile Include="SAP\Req\CreatePlanOrde.cs" />
    <Compile Include="SAP\Req\CreateProdOrder.cs" />
    <Compile Include="SAP\Req\CreatePruchase0rder.cs" />
    <Compile Include="SAP\Req\EditPlanOrder.cs" />
    <Compile Include="SAP\Req\LineQueryReq.cs" />
    <Compile Include="SAP\Res\ZFGWMS017_RES.cs" />
    <Compile Include="SAP\Res\ZFITFG001011_RES.cs" />
    <Compile Include="SAP\Res\ZFITFG001019_RES.cs" />
    <Compile Include="SAP\Res\ZFITFG001018_RES.cs" />
    <Compile Include="Sale\Req\HostOrderTreeReq.cs" />
    <Compile Include="Sale\Req\Sale_ShippingPlanListReq.cs" />
    <Compile Include="Sale\Req\SyncSaleDeliveryReq.cs" />
    <Compile Include="Sale\Sale_DeliveryScan.cs" />
    <Compile Include="Sale\SD_Host_OrderDetails.cs" />
    <Compile Include="Sale\SD_Sale_OrderDetails.cs" />
    <Compile Include="Sale\Sale_ShippingPlan.cs" />
    <Compile Include="Sale\TreeNode\TreeNode.cs" />
    <Compile Include="SAP\Req\CreatePlanOrderReq.cs" />
    <Compile Include="SAP\SAPRETURN.cs" />
    <Compile Include="SAP\View\SAP_T001L_View.cs" />
    <Compile Include="SAP\View\SAP_RESBM_View.cs" />
    <Compile Include="SAP\View\SAP_VBAK_View.cs" />
    <Compile Include="SAP\View\XZ_SAP_EKKO_EKPO_MARC_View.cs" />
    <Compile Include="SAP\View\SAP_EKKO_EKPO_View.cs" />
    <Compile Include="SAP\View\SAP_KNA1_View.cs" />
    <Compile Include="SAP\XZ_SAP_ANLA.cs" />
    <Compile Include="SAP\XZ_SAP_AUFK.cs" />
    <Compile Include="SAP\XZ_SAP_FICO002.cs" />
    <Compile Include="SAP\ZFGWMS017.cs" />
    <Compile Include="SAP\ZFITFG001019.cs" />
    <Compile Include="SAP\ZFITFG001018_ITEM.cs" />
    <Compile Include="SAP\ZFITFG001018.cs" />
    <Compile Include="SD\Import\SD_ShippingPlanImport.cs" />
    <Compile Include="SD\Parameters\SD_DeliveryScanParameters.cs" />
    <Compile Include="SD\Parameters\SD_ReturnScanParameters.cs" />
    <Compile Include="SD\Parameters\SD_ShippingPlanParameters.cs" />
    <Compile Include="SD\Sale_ShippingForOms.cs" />
    <Compile Include="SD\SD_DeliveryScanImport.cs" />
    <Compile Include="SD\SD_DeliveryScanPrint.cs" />
    <Compile Include="SD\SD_ShippingForOMS.cs" />
    <Compile Include="SD\ViewModel\SD_CustomerAdd_View.cs" />
    <Compile Include="SD\ViewModel\SD_DeliveryMain_View.cs" />
    <Compile Include="SD\ViewModel\SD_DeliveryScan_View.cs" />
    <Compile Include="SD\ViewModel\SD_NoDeliveryShippingPlan_View.cs" />
    <Compile Include="SD\ViewModel\SD_ReturnScanForVBAK_View.cs" />
    <Compile Include="SD\ViewModel\SD_ShippingPlanForVBAK_View.cs" />
    <Compile Include="SAP\XZ_SAP_CSKS.cs" />
    <Compile Include="SAP\XZ_SAP_KNA1.cs" />
    <Compile Include="SAP\XZ_SAP_SKA1.cs" />
    <Compile Include="SAP\XZ_SAP_VBAP.cs" />
    <Compile Include="SAP\XZ_SAP_VBAK.cs" />
    <Compile Include="SAP\XZ_SAP_EKKO.cs" />
    <Compile Include="SAP\XZ_SAP_EKPO.cs" />
    <Compile Include="SAP\SAPCustomer.cs" />
    <Compile Include="SAP\XZ_SAP_MARC.cs" />
    <Compile Include="SAP\XZ_SAP_T001L.cs" />
    <Compile Include="SAP\ZFGWMS001.cs" />
    <Compile Include="SAP\ZFGWMS001_1.cs" />
    <Compile Include="SAP\ZFGWMS002.cs" />
    <Compile Include="SAP\ZFGWMS002_1.cs" />
    <Compile Include="SAP\ZFGWMS003.cs" />
    <Compile Include="SAP\ZFGWMS004.cs" />
    <Compile Include="SAP\ZFGWMS005.cs" />
    <Compile Include="SAP\ZFGWMS006.cs" />
    <Compile Include="SAP\ZFGWMS008.cs" />
    <Compile Include="SAP\ZFGWMS009.cs" />
    <Compile Include="SAP\ZFGWMS010.cs" />
    <Compile Include="SAP\ZFGWMS011.cs" />
    <Compile Include="SAP\ZFGWMS012.cs" />
    <Compile Include="SAP\ZFGWMS013.cs" />
    <Compile Include="SAP\ZFGWMS014.cs" />
    <Compile Include="SAP\ZFGWMS015.cs" />
    <Compile Include="SAP\ZFGWMS016.cs" />
    <Compile Include="SAP\ZFGWMS018.cs" />
    <Compile Include="SD\Parameters\SD_ConsignmentNoteParameters.cs" />
    <Compile Include="SD\SD_ConsignmentNoteDetail.cs" />
    <Compile Include="SD\SD_ShippingPlanDetail.cs" />
    <Compile Include="SD\SD_ShippingPlan.cs" />
    <Compile Include="SD\SAP_SD_Task.cs" />
    <Compile Include="SD\SAP_SD_TaskDetail.cs" />
    <Compile Include="SD\SD_DeliveryScan.cs" />
    <Compile Include="SD\SD_ReturnScan.cs" />
    <Compile Include="SD\SD_ConsignmentNote.cs" />
    <Compile Include="SD\ViewModel\SD_ConsignmentNote_View.cs" />
    <Compile Include="SRM\P_InspectionDetail.cs" />
    <Compile Include="SRM\ViewModel\SRM_P_InspectionDetail_View.cs" />
    <Compile Include="SRM\ViewModel\SRM_S_SupplierInfo_View.cs" />
    <Compile Include="Sys\Sys_ApiLogConfig.cs" />
    <Compile Include="Sys\Sys_AppVersion.cs" />
    <Compile Include="Sys\Sys_DbBackup.cs" />
    <Compile Include="Sys\Sys_DbBackupConfig.cs" />
    <Compile Include="Sys\Sys_Log.cs" />
    <Compile Include="Sys\Sys_Mail.cs" />
    <Compile Include="Sys\Sys_MailServerConfig.cs" />
    <Compile Include="Sys\Sys_Message.cs" />
    <Compile Include="Sys\Sys_MessageNotifySetting.cs" />
    <Compile Include="Sys\Sys_MessageType.cs" />
    <Compile Include="Sys\Sys_Organization.cs" />
    <Compile Include="Sys\Sys_Dictionary.cs" />
    <Compile Include="Sys\Sys_Resource.cs" />
    <Compile Include="Sys\Sys_Role.cs" />
    <Compile Include="Sys\Sys_RoleResource.cs" />
    <Compile Include="Sys\Sys_SAPCompanyInfo.cs" />
    <Compile Include="Sys\Sys_SwithConfig.cs" />
    <Compile Include="Sys\Sys_User.cs" />
    <Compile Include="Sys\Sys_UserMessage.cs" />
    <Compile Include="Sys\Sys_UserRole.cs" />
    <Compile Include="Sys\Sys_UserSapAccount.cs" />
    <Compile Include="Sys\ViewModel\BatchSettingRequestParmsBase.cs" />
    <Compile Include="Sys\ViewModel\RequestBatchProcess.cs" />
    <Compile Include="Sys\ViewModel\SysUserForRole_View.cs" />
    <Compile Include="Sys\ViewModel\Sys_MessageNotifySettingInput.cs" />
    <Compile Include="UniqueCodeAttribute.cs" />
    <Compile Include="Utils\DateUtil.cs" />
    <Compile Include="VW\PROC_RPT_BarCodeRetrospect.cs" />
    <Compile Include="VW\V_BarCode.cs" />
    <Compile Include="VW\V_NotPostStockMove.cs" />
    <Compile Include="YS\CaView.cs" />
  </ItemGroup>
  <ItemGroup>
    <None Include="App.config" />
    <None Include="packages.config">
      <SubType>Designer</SubType>
    </None>
  </ItemGroup>
  <ItemGroup>
    <Service Include="{508349B6-6B84-4DF5-91F0-309BEEBAD82D}" />
  </ItemGroup>
  <ItemGroup>
    <Folder Include="Util\" />
  </ItemGroup>
  <Import Project="$(MSBuildToolsPath)\Microsoft.CSharp.targets" />
</Project>