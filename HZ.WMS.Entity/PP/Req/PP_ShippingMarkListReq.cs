using System;

namespace HZ.WMS.Entity.PP
{
    /// <summary>
    /// 唛头打印查询请求
    /// </summary>
    public class PP_ShippingMarkListReq
    {
        /// <summary>
        /// 合同号
        /// </summary>
        public string ContractNo { get; set; }

        /// <summary>
        /// 出厂编号
        /// </summary>
        public string FactoryNo { get; set; }

        /// <summary>
        /// 装配日期开始时间
        /// </summary>
        public DateTime? AssemblyStartDate { get; set; }

        /// <summary>
        /// 装配日期结束时间
        /// </summary>
        public DateTime? AssemblyEndDate { get; set; }

        /// <summary>
        /// 打印状态
        /// </summary>
        public bool? PrintStatus { get; set; }

        /// <summary>
        /// 唛头打印系统
        /// </summary>
        public string PrintSystem { get; set; }

        /// <summary>
        /// 是否出口
        /// </summary>
        public string IsExport { get; set; }

        /// <summary>
        /// 装配线号
        /// </summary>
        public string AssemblyLine { get; set; }

        /// <summary>
        /// 发货单位
        /// </summary>
        public string DeliveryCustomer { get; set; }

        /// <summary>
        /// 订单号
        /// </summary>
        public string OrderNo { get; set; }

        /// <summary>
        /// 套袋/塑封（箱板刷字）
        /// </summary>
        public string BoxBoardBrushing { get; set; }

        /// <summary>
        /// 打印方向
        /// </summary>
        public string PrintDirection { get; set; }
        
        /// <summary>
        /// 参数检查
        /// </summary>
        public bool? ParameterCheck { get; set; }
    }
}
