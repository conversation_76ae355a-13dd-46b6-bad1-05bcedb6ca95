using System;

namespace HZ.WMS.Entity.PP
{
    /// <summary>
    /// 唛头打印记录查询请求
    /// </summary>
    public class PP_ShippingMarkPrintRecordListReq
    {
        /// <summary>
        /// 唛头ID
        /// </summary>
        public string ShippingMarkId { get; set; }

        /// <summary>
        /// 模板名称
        /// </summary>
        public string TemplateName { get; set; }

        /// <summary>
        /// 纸张方向
        /// </summary>
        public string PrintDirection { get; set; }

        /// <summary>
        /// 纸张大小
        /// </summary>
        public string PaperSize { get; set; }

        /// <summary>
        /// 纸张类别
        /// </summary>
        public string PaperType { get; set; }

        /// <summary>
        /// 创建开始时间
        /// </summary>
        public DateTime? CreateStartTime { get; set; }

        /// <summary>
        /// 创建结束时间
        /// </summary>
        public DateTime? CreateEndTime { get; set; }

        /// <summary>
        /// 创建用户
        /// </summary>
        public string CUser { get; set; }

        /// <summary>
        /// 关键字（支持模糊查询唛头ID、模板名称、创建用户）
        /// </summary>
        public string Keyword { get; set; }
    }
}
