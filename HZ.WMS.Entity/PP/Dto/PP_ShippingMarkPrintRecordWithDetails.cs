using System;
using System.ComponentModel;

namespace HZ.WMS.Entity.PP.Dto
{
    /// <summary>
    /// 唛头打印记录包含详细信息
    /// </summary>
    public class PP_ShippingMarkPrintRecordWithDetails
    {
        /// <summary>
        /// 主键ID
        /// </summary>
        [Description("主键ID")]
        public string Id { get; set; }

        /// <summary>
        /// 唛头ID
        /// </summary>
        [Description("唛头ID")]
        public string ShippingMarkId { get; set; }

        /// <summary>
        /// 模板名称
        /// </summary>
        [Description("模板名称")]
        public string TemplateName { get; set; }

        /// <summary>
        /// 纸张方向
        /// </summary>
        [Description("纸张方向")]
        public string PrintDirection { get; set; }

        /// <summary>
        /// 纸张大小
        /// </summary>
        [Description("纸张大小")]
        public string PaperSize { get; set; }

        /// <summary>
        /// 纸张类别
        /// </summary>
        [Description("纸张类别")]
        public string PaperType { get; set; }

        /// <summary>
        /// 备注
        /// </summary>
        [Description("备注")]
        public string Remark { get; set; }

        /// <summary>
        /// 创建用户
        /// </summary>
        [Description("创建用户")]
        public string CUser { get; set; }

        /// <summary>
        /// 创建时间
        /// </summary>
        [Description("创建时间")]
        public DateTime? CTime { get; set; }

        /// <summary>
        /// 修改用户
        /// </summary>
        [Description("修改用户")]
        public string MUser { get; set; }

        /// <summary>
        /// 修改时间
        /// </summary>
        [Description("修改时间")]
        public DateTime? MTime { get; set; }

        // 关联的唛头信息
        /// <summary>
        /// 出厂编号
        /// </summary>
        [Description("出厂编号")]
        public string FactoryNo { get; set; }

        /// <summary>
        /// 合同号
        /// </summary>
        [Description("合同号")]
        public string ContractNo { get; set; }

        /// <summary>
        /// 订单号
        /// </summary>
        [Description("订单号")]
        public string OrderNo { get; set; }

        /// <summary>
        /// 发货单位
        /// </summary>
        [Description("发货单位")]
        public string DeliveryCustomer { get; set; }

        /// <summary>
        /// 生产型号
        /// </summary>
        [Description("生产型号")]
        public string ProduceModel { get; set; }

        /// <summary>
        /// 装配线号
        /// </summary>
        [Description("装配线号")]
        public string AssemblyLine { get; set; }

        /// <summary>
        /// 装配日期
        /// </summary>
        [Description("装配日期")]
        public DateTime? AssemblyDate { get; set; }

        /// <summary>
        /// SAP生产订单号
        /// </summary>
        [Description("SAP生产订单号")]
        public string SapProduceNo { get; set; }
    }
}
