using SqlSugar;
using System.ComponentModel;

namespace HZ.WMS.Entity.PP
{
    /// <summary>
    /// 唛头打印记录
    /// </summary>
    [SugarTable("PP_ShippingMarkPrintRecord")]
    public class PP_ShippingMarkPrintRecord : BaseEntity
    {
        /// <summary>
        /// 主键ID
        /// </summary>
        [SugarColumn(IsPrimaryKey = true)]
        [Description("主键ID")]
        public string Id { get; set; }

        /// <summary>
        /// 唛头ID
        /// </summary>
        [Description("唛头ID")]
        [SugarColumn(Length = 36)]
        public string ShippingMarkId { get; set; }

        /// <summary>
        /// 模板名称
        /// </summary>
        [Description("模板名称")]
        [SugarColumn(Length = 32)]
        public string TemplateName { get; set; }

        /// <summary>
        /// 纸张方向
        /// </summary>
        [Description("纸张方向")]
        [SugarColumn(Length = 32)]
        public string PrintDirection { get; set; }

        /// <summary>
        /// 纸张大小
        /// </summary>
        [Description("纸张大小")]
        [SugarColumn(Length = 32)]
        public string PaperSize { get; set; }

        /// <summary>
        /// 纸张类别
        /// </summary>
        [Description("纸张类别")]
        [SugarColumn(Length = 32)]
        public string PaperType { get; set; }
    }
}
