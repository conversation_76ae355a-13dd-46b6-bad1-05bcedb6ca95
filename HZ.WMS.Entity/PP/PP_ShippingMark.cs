using SqlSugar;
using System;
using System.ComponentModel;
using HZ.WMS.Entity.MD;

namespace HZ.WMS.Entity.PP
{
    /// <summary>
    /// 唛头打印
    /// </summary>
    [SugarTable("PP_ShippingMark")]
    public class PP_ShippingMark : BaseEntity
    {
        /// <summary>
        /// Id
        /// </summary>
        [SugarColumn(IsPrimaryKey = true)]
        [Description("Id")]
        public string Id { get; set; }

        /// <summary>
        /// 打印状态
        /// </summary>
        [Description("打印状态")]
        public bool? PrintStatus { get; set; }

        /// <summary>
        /// 打印系统
        /// </summary>
        [Description("打印系统")]
        [SugarColumn(Length = 64)]
        public string PrintSystem { get; set; }

        /// <summary>
        /// 纸张大小
        /// </summary>
        [Description("纸张大小")]
        [SugarColumn(Length = 32)]
        public string PaperSize { get; set; }

        /// <summary>
        /// 纸张类别
        /// </summary>
        [Description("纸张类别")]
        [SugarColumn(Length = 32)]
        public string PaperType { get; set; }

        /// <summary>
        /// SAP生产订单号
        /// </summary>
        [Description("SAP生产订单号")]
        [SugarColumn(Length = 64)]
        public string SapProduceNo { get; set; }

        /// <summary>
        /// 出厂编号
        /// </summary>
        [Description("出厂编号")]
        [SugarColumn(Length = 64)]
        public string FactoryNo { get; set; }

        /// <summary>
        /// 订单号
        /// </summary>
        [Description("订单号")]
        [SugarColumn(Length = 64)]
        public string OrderNo { get; set; }

        /// <summary>
        /// 合同号
        /// </summary>
        [Description("合同号")]
        [SugarColumn(Length = 64)]
        public string ContractNo { get; set; }

        /// <summary>
        /// 发货单位
        /// </summary>
        [Description("发货单位")]
        [SugarColumn(Length = 128)]
        public string DeliveryCustomer { get; set; }

        /// <summary>
        /// 产品型号
        /// </summary>
        [Description("产品型号")]
        [SugarColumn(Length = 128)]
        public string ProduceModel { get; set; }

        /// <summary>
        /// 产品件号
        /// </summary>
        [Description("产品件号")]
        [SugarColumn(Length = 64)]
        public string ProducePart { get; set; }

        /// <summary>
        /// 装配线号
        /// </summary>
        [Description("装配线号")]
        [SugarColumn(Length = 32)]
        public string AssemblyLine { get; set; }

        /// <summary>
        /// 装配日期
        /// </summary>
        [Description("装配日期")]
        public DateTime? AssemblyDate { get; set; }

        /// <summary>
        /// 额定载重
        /// </summary>
        [Description("额定载重")]
        [SugarColumn(Length = 32)]
        public string RatedLoad { get; set; }

        /// <summary>
        /// 额定速度
        /// </summary>
        [Description("额定速度")]
        [SugarColumn(Length = 32)]
        public string RatedSpeed { get; set; }

        /// <summary>
        /// 额定电压
        /// </summary>
        [Description("额定电压")]
        [SugarColumn(Length = 32)]
        public string RatedVoltage { get; set; }

        /// <summary>
        /// 铭牌要求
        /// </summary>
        [Description("铭牌要求")]
        [SugarColumn(Length = 256)]
        public string NameplateRequirements { get; set; }

        /// <summary>
        /// 曳引比
        /// </summary>
        [Description("曳引比")]
        [SugarColumn(Length = 32)]
        public string TractionRatio { get; set; }

        /// <summary>
        /// 客户型号
        /// </summary>
        [Description("客户型号")]
        [SugarColumn(Length = 128)]
        public string CustomerModel { get; set; }

        /// <summary>
        /// 箱板刷字
        /// </summary>
        [Description("箱板刷字")]
        [SugarColumn(Length = 256)]
        public string BoxBoardBrushing { get; set; }

        /// <summary>
        /// 是否出口
        /// </summary>
        [Description("是否出口")]
        public string IsExport { get; set; }

        /// <summary>
        /// 项目名称
        /// </summary>
        [Description("项目名称")]
        [SugarColumn(Length = 256)]
        public string ProjectName { get; set; }

        /// <summary>
        /// 接受编号
        /// </summary>
        [Description("接受编号")]
        [SugarColumn(Length = 64)]
        public string AcceptNo { get; set; }

        /// <summary>
        /// 数量
        /// </summary>
        [Description("数量")]
        public int? Quantity { get; set; }

        /// <summary>
        /// 制动器电压
        /// </summary>
        [Description("制动器电压")]
        [SugarColumn(Length = 32)]
        public string BrakeVoltage { get; set; }

        /// <summary>
        /// SAP销售订单号
        /// </summary>
        [Description("SAP销售订单号")]
        [SugarColumn(Length = 64)]
        public string SapSalesOrderNo { get; set; }

        /// <summary>
        /// SAP销售订单行号
        /// </summary>
        [Description("SAP销售订单行号")]
        [SugarColumn(Length = 32)]
        public string SapSalesOrderLineNo { get; set; }

        /// <summary>
        /// 生产管理员
        /// </summary>
        [Description("生产管理员")]
        [SugarColumn(Length = 64)]
        public string ProductionManager { get; set; }

        /// <summary>
        /// 装箱尺寸
        /// </summary>
        [Description("装箱尺寸")]
        [SugarColumn(Length = 128)]
        public string PackingSize { get; set; }

        /// <summary>
        /// 装箱净重
        /// </summary>
        [Description("装箱净重")]
        [SugarColumn(Length = 32)]
        public string PackingNetWeight { get; set; }

        /// <summary>
        /// 装箱毛重
        /// </summary>
        [Description("装箱毛重")]
        [SugarColumn(Length = 32)]
        public string PackingGrossWeight { get; set; }

        /// <summary>
        /// 功率
        /// </summary>
        [Description("额定功率")]
        [SugarColumn(Length = 32)]
        public string RatedPower { get; set; }

        /// <summary>
        /// 节径
        /// </summary>
        [Description("节径")]
        [SugarColumn(Length = 32)]
        public string PitchDiameter { get; set; }

        /// <summary>
        /// 绳槽
        /// </summary>
        [Description("绳槽")]
        [SugarColumn(Length = 32)]
        public string RopeGroove { get; set; }

        /// <summary>
        /// 槽距
        /// </summary>
        [Description("槽距")]
        [SugarColumn(Length = 32)]
        public string GrooveDistance { get; set; }

        /// <summary>
        /// 唛头打印时间
        /// </summary>
        [Description("唛头打印时间")]
        public DateTime? ShippingMarkPrintTime { get; set; }

        /// <summary>
        /// 清单打印状态
        /// </summary>
        [Description("清单打印状态")]
        public bool? ListPrintStatus { get; set; }

        /// <summary>
        /// 清单打印时间
        /// </summary>
        [Description("清单打印时间")]
        public DateTime? ListPrintTime { get; set; }
        
        /// <summary>
        /// 打印模板ID
        /// </summary>
        [Description("打印模板ID")]
        public string PrintTemplateId { get; set; }
        
        /// <summary>
        /// 打印方向
        /// </summary>
        [Description("打印方向")]
        public string PrintDirection { get; set; }
        
        /// <summary>
        /// 唛头封装方式
        /// </summary>
        [Description("唛头封装方式")]
        public string EncapsulationMethod { get; set; }
        
        /// <summary>
        /// 参数检查
        /// </summary>
        [Description("参数检查")]
        public bool? ParameterCheck { get; set; }

        /// <summary>
        /// 基本规格参数信息
        /// </summary>
        [Description("基本规格参数信息")]
        [SugarColumn(IsIgnore = true)]
        public MD_BasicSpecification BasicSpecification { get; set; }
    }
}
