using System;
using System.Collections.Generic;
using System.Configuration;
using System.Data;
using System.IO;
using System.Linq;
using System.Net;
using System.Net.Http;
using System.Net.Http.Headers;
using System.Web.Http;
using AOS.OMS.Application.Util;
using AOS.OMS.Entity.Sale;
using HZ.Core.Http;
using HZ.Core.Log;
using HZ.Core.Office;
using HZ.WMS.Application.SD;
using HZ.WMS.Entity.Produce.Req;
using HZ.WMS.Entity.Produce.Res;
using HZ.WMS.Entity.SD;
using HZ.WMS.WebAPI.Controllers;
using Newtonsoft.Json;
using NPOI.SS.UserModel;
using NPOI.SS.Util;
using NPOI.XSSF.UserModel;

namespace HZ.WMS.WebAPI.Areas.SD.Controllers
{
    /// <summary>
    /// 发运计划
    /// </summary>
    public class Produce_SchedulingController : ApiBaseController
    {
        #region 初始化

        private Produce_ShedulingApp _app = new Produce_ShedulingApp();

        #endregion

        #region 查询分页列表

        /// <summary>
        /// 查询分页列表
        /// </summary>
        /// <param name="page"></param>
        /// <param name="req"></param>
        /// <returns></returns>
        [HttpPost]
        public IHttpActionResult GetPageList([FromUri] Pagination page, [FromBody] Produce_SchedulingListReq req)
        {
            var result = new ResponseData();
            try
            {
                var itemsData = _app.GetPageList(page, req);
                result.Data = new ResponsePageData { total = page.Total, items = itemsData };
                result.Code = (int)WMSStatusCode.Success;
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }

            return Json(result);
        }

        #endregion

        #region 查询统计树

        [HttpGet]
        public IHttpActionResult GetTree([FromUri] Produce_SchedulingListReq req, [FromUri] HostOrderTreeReq treeReq)
        {
            var result = new ResponseData();
            try
            {
                result.Data = _app.GetTree(req, treeReq);
                result.Code = (int)WMSStatusCode.Success;
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }

            return Json(result);
        }

        #endregion

        #region 查询统计树 (不含线体)

        [HttpGet]
        public IHttpActionResult GetTreeNotLine([FromUri] Produce_SchedulingListReq req, [FromUri] HostOrderTreeReq treeReq)
        {
            var result = new ResponseData();
            try
            {
                result.Data = _app.GetTreeNotLine(req, treeReq);
                result.Code = (int)WMSStatusCode.Success;
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }

            return Json(result);
        }

        #endregion

        #region 查询统计树（装配日期开始）

        /// <summary>
        /// 查询统计树（排产日期开始）
        /// </summary>
        /// <param name="req">排产列表请求参数</param>
        /// <param name="treeReq">树请求参数</param>
        /// <returns>树节点列表</returns>
        [HttpGet]
        public IHttpActionResult GetTreeBySchedulingDate([FromUri] Produce_SchedulingListReq req, [FromUri] HostOrderTreeReq treeReq)
        {
            var result = new ResponseData();
            try
            {
                result.Data = _app.GetTreeBySchedulingDate(req, treeReq);
                result.Code = (int)WMSStatusCode.Success;
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }

            return Json(result);
        }

        #endregion

        #region 查询生产版本列表

        /// <summary>
        /// 查询分页列表
        /// </summary>
        /// <param name="page"></param>
        /// <param name="req"></param>
        /// <returns></returns>
        [HttpGet]
        public IHttpActionResult GetProduceVersionList([FromUri] Produce_VersionListReq req)
        {
            var result = new ResponseData();
            try
            {
                //调用生成发运的WMS接口
                string queryMatnrVersionUri = ConfigurationManager.AppSettings["SapUrl"] + ConfigurationManager.AppSettings["GetMatnrVersion"];
                string token = ConfigurationManager.AppSettings["SapToken"];
                LineQueryReq lineQueryReq = new LineQueryReq();
                lineQueryReq.WERKS = "2002";
                lineQueryReq.MATNR = req.MaterialCode;
                lineQueryReq.ADATU = DateTime.Now.ToString("yyyyMMdd");
                List<LineQueryReq> lineQueries = new List<LineQueryReq>();
                lineQueries.Add(lineQueryReq);
                string postRst = HttpUtil.HttpPost(queryMatnrVersionUri + "?token=" + token, JsonConvert.SerializeObject(lineQueries), "POST");
                dynamic res = JsonConvert.DeserializeObject(postRst);
                result.Data = res;
                result.Code = (int)WMSStatusCode.Success;
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }

            return Json(result);
        }

        #endregion

        #region 排产日期设置

        [HttpPost]
        public IHttpActionResult SetProduceSchedulingDate([FromBody] SetProduceSchedulingDateReq req)
        {
            var result = new ResponseData();
            try
            {
                string resMsg = string.Empty;
                var rst = _app.SetProduceSchedulingDate(req, out resMsg);
                if (rst)
                {
                    result.Code = (int)WMSStatusCode.Success;
                    result.Message = "";
                }
                else
                {
                    result.Code = (int)WMSStatusCode.Failed;
                    result.Message = resMsg;
                }
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }

            return Json(result);
        }

        #endregion

        #region 生产线体设置

        [HttpPost]
        public IHttpActionResult SetProduceLine([FromBody] SetProduceLineReq req)
        {
            var result = new ResponseData();
            try
            {
                string resMsg = string.Empty;
                var rst = _app.SetProduceLine(req, out resMsg);
                if (rst)
                {
                    result.Code = (int)WMSStatusCode.Success;
                    result.Message = "";
                }
                else
                {
                    result.Code = (int)WMSStatusCode.Failed;
                    result.Message = resMsg;
                }
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }

            return Json(result);
        }

        #endregion

        #region 完成预排产

        [HttpPost]
        public IHttpActionResult CompletePreProduce([FromBody] CompletePreProduceReq req)
        {
            var result = new ResponseData();
            try
            {
                string resMsg = string.Empty;
                var rst = _app.CompletePreProduce(req, GetCurrentUser(), out resMsg);
                if (rst)
                {
                    result.Code = (int)WMSStatusCode.Success;
                    result.Message = resMsg;
                }
                else
                {
                    result.Code = (int)WMSStatusCode.Failed;
                    result.Message = resMsg;
                }
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }

            return Json(result);
        }

        #endregion

        #region 设置序号&批次

        [HttpPost]
        public IHttpActionResult SetSortAndBatch([FromBody] SetSortAndBatchReq[] req)
        {
            var result = new ResponseData();
            try
            {
                string resMsg = string.Empty;
                var rst = _app.SetSortAndBatch(req, out resMsg);
                if (rst)
                {
                    result.Code = (int)WMSStatusCode.Success;
                    result.Message = "";
                }
                else
                {
                    result.Code = (int)WMSStatusCode.Failed;
                    result.Message = resMsg;
                }
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }

            return Json(result);
        }

        #endregion

        #region 确认正式排产

        [HttpPost]
        public IHttpActionResult ConfirmFormalProduce([FromBody] string[] ids)
        {
            var result = new ResponseData();
            try
            {
                string resMsg = string.Empty;
                var rst = _app.ConfirmFormalProduce(ids, out resMsg, GetCurrentUser());
                if (rst)
                {
                    // 成功后，创建报工基础信息
                    // var reportApp = new Produce_ReportApp();
                    // bool createResult = reportApp.CreateInitialReportInfo(ids, GetCurrentUser().UserName);
                    
                    result.Code = (int)WMSStatusCode.Success;
                    // result.Message = resMsg + (createResult ? "，已创建报工信息" : "，创建报工信息失败");
                    result.Message = resMsg;
                }
                else
                {
                    result.Code = (int)WMSStatusCode.Failed;
                    result.Message = resMsg;
                }
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }

            return Json(result);
        }

        #endregion

        #region 排产序号生成

        [HttpPost]
        public IHttpActionResult GenerationProduceSort([FromBody] GenerationProduceSortReq req)
        {
            var result = new ResponseData();
            try
            {
                string resMsg = string.Empty;
                var rst = _app.GenerationProduceSort(req, out resMsg);
                if (rst)
                {
                    result.Code = (int)WMSStatusCode.Success;
                    result.Message = "";
                }
                else
                {
                    result.Code = (int)WMSStatusCode.Failed;
                    result.Message = resMsg;
                }
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }

            return Json(result);
        }

        #endregion

        #region 导出

        /// <summary>
        /// 导出
        /// </summary>
        /// <param name="keyword"></param>
        /// <param name="dateValue"></param>
        /// <returns></returns>
        [HttpGet]
        public IHttpActionResult ExportToExcelFile([FromUri] Produce_SchedulingListReq req, [FromUri] int type)
        {
            var result = new ResponseData();
            try
            {
                var itemDataList = _app.GetExportInfo(req);

                // 获取SaleBom数据（此部分逻辑不变）
                string queryMatnrVersionUri = ConfigurationManager.AppSettings["SapUrl"] + ConfigurationManager.AppSettings["GetSaleBom"];
                string token = ConfigurationManager.AppSettings["SapToken"];
                List<SaleBomReq> lineQueries = itemDataList.Select(produce => new SaleBomReq
                {
                    WERKS = "2002",
                    VBELN = produce.SaleSapNo,
                    POSNR = produce.SaleSapLine?.ToString("D6")
                }).ToList().Where(t => !string.IsNullOrEmpty(t.VBELN)).ToList();

                string postRst = HttpUtil.HttpPost($"{queryMatnrVersionUri}?token={token}", JsonConvert.SerializeObject(lineQueries), "POST");
                var saleBomList = JsonConvert.DeserializeObject<List<SaleBomRes>>(postRst);

                var bomDict = saleBomList
                    .GroupBy(b => new { b.VBELN, b.POSNR })
                    .ToDictionary(g => g.Key, g => g.ToList());

                foreach (var produce in itemDataList)
                {
                    var key = new { VBELN = produce.SaleSapNo, POSNR = produce.SaleSapLine?.ToString("D6") };
                    produce.SaleBoms = bomDict.TryGetValue(key, out var boms) ? boms : new List<SaleBomRes>();
                }

                // 按ProduceLine分组
                var groupedItems = itemDataList.GroupBy(p => p.ProduceLine).ToList();

                byte[] excelData;
                using (var memoryStream = new MemoryStream())
                {
                    IWorkbook workbook = new XSSFWorkbook();

                    /******************** 新增：创建第一个Sheet（生产指令单） ********************/
                    ISheet instructionSheet = workbook.CreateSheet("生产指令单");
                    instructionSheet.DefaultColumnWidth = 20;

                    // 1. 第一行：合并单元格显示公司名称
                    IRow companyRow = instructionSheet.CreateRow(0);
                    companyRow.CreateCell(0).SetCellValue("浙江西子富沃德电机有限公司");
                    instructionSheet.AddMergedRegion(new CellRangeAddress(0, 0, 0, 40)); // 合并所有列

                    // 2. 第二行：动态生成标题日期
                    string dynamicTitle = "FWD 主机生产指令单"; // 默认值
                    if (itemDataList.Any() && itemDataList[0].DeliveryDate.HasValue)
                    {
                        DateTime deliveryDate = itemDataList[0].DeliveryDate.Value;
                        DateTime planDate = deliveryDate.AddDays(-2);
                        dynamicTitle = $"FWD {planDate:yyyy-MM-dd}主机生产指令单(车间{deliveryDate:yyyy-MM-dd}装配)";
                    }

                    IRow titleRow = instructionSheet.CreateRow(1);
                    titleRow.CreateCell(0).SetCellValue(dynamicTitle);
                    instructionSheet.AddMergedRegion(new CellRangeAddress(1, 1, 0, 40));

                    // 3. 第三行：表头
                    var headers = new List<string>
                    {
                        "顺序号", "装配线号", "定单号", "合同号", "发货单位", "交货时间", "产品型号", "产品件号",
                        "富沃德机型", "盘车装置", "制动器电压", "连接方式", "编码器型号", "编码器线长", "软管", "变频器", "护 罩",
                        "远程松闸", "松闸线长", "油 漆", "数量", "额定载重", "额定速度", "额定电压", "铭牌要求", "曳引比", "客户型号",
                        "箱板刷字", "是否是出口梯", "项目名称", "备 注", "接受编号", "入库时间", "出厂编号", "批次", "主机未完成原因",
                        "部件代码", "上行超速保护代码", "意外移动保护代码", "制动器编号1", "制动器编号2"
                    };
                    IRow headerRow1 = instructionSheet.CreateRow(2);
                    for (int i = 0; i < headers.Count; i++)
                    {
                        headerRow1.CreateCell(i).SetCellValue(headers[i]);
                    }

                    // 4. 填充数据行
                    int dataRowIndex = 3;
                    foreach (var item in itemDataList)
                    {
                        IRow dataRow = instructionSheet.CreateRow(dataRowIndex++);
                        int col = 0;
                        // 序号
                        dataRow.CreateCell(col++).SetCellValue(dataRowIndex - 3);
                        // 装配线号
                        dataRow.CreateCell(col++).SetCellValue(item.ProduceLine ?? "");
                        // 定单号
                        dataRow.CreateCell(col++).SetCellValue(item.SaleSapNo ?? "");
                        // 合同号
                        dataRow.CreateCell(col++).SetCellValue(item.ContractNo ?? "");
                        // 发货单位
                        dataRow.CreateCell(col++).SetCellValue(item.CustomerName ?? "");
                        // 交货时间
                        dataRow.CreateCell(col++).SetCellValue(item.DeliveryDate?.ToString("yyyy-MM-dd") ?? "");
                        // 产品型号
                        dataRow.CreateCell(col++).SetCellValue(item.ProduceModel ?? "");
                        // 产品件号
                        dataRow.CreateCell(col++).SetCellValue(item.ItemCode ?? "");
                        // 富沃德机型（留空）
                        dataRow.CreateCell(col++).SetCellValue(item.ProduceModel ?? "");
                        // 盘车装置
                        dataRow.CreateCell(col++).SetCellValue(item.TurningGear ?? "");
                        // 制动器电压
                        dataRow.CreateCell(col++).SetCellValue(item.BrakeVoltage ?? "");
                        // 连接方式（留空）
                        dataRow.CreateCell(col++).SetCellValue(item.JunctionMethod ?? "");
                        // 编码器型号（留空）
                        dataRow.CreateCell(col++).SetCellValue(item.Encoder ?? "");
                        // 编码器线长（留空）
                        dataRow.CreateCell(col++).SetCellValue(item.EncoderLineLength ?? "");
                        // 软管（留空）
                        dataRow.CreateCell(col++).SetCellValue(item.Hose ?? "");
                        // 变频器（从SaleBoms中获取）
                        dataRow.CreateCell(col++).SetCellValue(item.FrequencyTransformer ?? "");
                        // 护罩（留空）
                        dataRow.CreateCell(col++).SetCellValue(item.HostCover ?? "");
                        // 远程松闸（固定值）
                        dataRow.CreateCell(col++).SetCellValue(item.RemoteDeclutch ?? "");
                        // 松闸线长（留空）
                        dataRow.CreateCell(col++).SetCellValue(item.DeclutchLineLength ?? "");
                        // 油漆（固定值）
                        dataRow.CreateCell(col++).SetCellValue(item.HostColor ?? "");
                        // 数量
                        dataRow.CreateCell(col++).SetCellValue(item.Quantity ?? 0);
                        // 额定载重
                        dataRow.CreateCell(col++).SetCellValue(item.RatedLoad ?? "");
                        // 额定速度
                        dataRow.CreateCell(col++).SetCellValue(item.RatedElevatorSpeed ?? "");
                        // 额定电压
                        dataRow.CreateCell(col++).SetCellValue(item.RatedVoltage ?? "");
                        // 铭牌要求
                        dataRow.CreateCell(col++).SetCellValue(item.HostNameplate ?? "");
                        // 曳引比
                        dataRow.CreateCell(col++).SetCellValue(item.TractionRatio ?? "");
                        // 客户型号
                        dataRow.CreateCell(col++).SetCellValue(item.CustomerModel ?? "");
                        // 箱板刷字
                        dataRow.CreateCell(col++).SetCellValue(item.Brush ?? "");
                        // 是否出口
                        dataRow.CreateCell(col++).SetCellValue(item.IsExport ?? "");
                        // 项目名称
                        dataRow.CreateCell(col++).SetCellValue(item.ProjectName ?? "");
                        // 备 注
                        dataRow.CreateCell(col++).SetCellValue(item.OrderRemark ?? "");
                        // 接受编号
                        dataRow.CreateCell(col++).SetCellValue(item.NonstandardNo ?? "");
                        // 入库时间
                        dataRow.CreateCell(col++).SetCellValue(item.InStoreTime != null ? item.InStoreTime.Value.ToString("yyyy-MM-dd") : "");
                        // 出厂编号
                        dataRow.CreateCell(col++).SetCellValue(item.FactoryNo ?? "");
                        // 批次
                        dataRow.CreateCell(col++).SetCellValue(item.BatchNo ?? "");
                        // 主机未完成原因
                        col++;
                        // 部件代码
                        dataRow.CreateCell(col++).SetCellValue(item.PartCode ?? "");
                        // 上行超速保护代码
                        col++;
                        dataRow.CreateCell(col++).SetCellValue(item.UpOverSpeedCode ?? "");
                        // 意外移动保护代码
                        col++;
                        dataRow.CreateCell(col++).SetCellValue(item.AccidentMoveCode ?? "");
                        // 制动器编号1
                        col++;
                        // 制动器编号2
                        col++;
                    }

                    // 自动调整列宽
                    for (int i = 0; i < headers.Count; i++)
                    {
                        instructionSheet.AutoSizeColumn(i);
                        if (instructionSheet.GetColumnWidth(i) < 3000)
                        {
                            instructionSheet.SetColumnWidth(i, 3000);
                        }
                    }


                    foreach (var group in groupedItems)
                    {
                        string produceLine = group.Key;
                        var itemsInGroup = group.ToList();

                        // 动态列：基于当前分组的extwg生成
                        var allExtwgs = itemsInGroup
                            .SelectMany(p => p.SaleBoms.Select(b => b.EWBEZ))
                            .Distinct()
                            .OrderBy(e => e)
                            .ToList();

                        // 构建DataTable（每个分组独立）
                        DataTable dt = new DataTable(produceLine);

                        // 固定列（保持不变）
                        dt.Columns.Add("合同号");
                        dt.Columns.Add("出厂编号");
                        dt.Columns.Add("客户");
                        dt.Columns.Add("批次");
                        dt.Columns.Add("装配线");
                        dt.Columns.Add("接收编号");
                        dt.Columns.Add("远程松闸");
                        dt.Columns.Add("盘车齿轮");
                        dt.Columns.Add("制动器电压");
                        dt.Columns.Add("油漆");
                        dt.Columns.Add("主机型号");

                        // 动态列处理
                        Dictionary<string, string> numberMap = new Dictionary<string, string>();
                        int num = 1;
                        foreach (var extwg in allExtwgs)
                        {
                            dt.Columns.Add(extwg);
                            string qtyCol = "数量" + num;
                            numberMap.Add(extwg, qtyCol);
                            dt.Columns.Add(qtyCol);
                            num++;
                        }

                        // 填充数据行
                        foreach (var produce in itemsInGroup)
                        {
                            DataRow row = dt.NewRow();

                            // 固定列数据
                            row["合同号"] = produce.ContractNo ?? "";
                            row["出厂编号"] = produce.FactoryNo ?? "";
                            row["客户"] = produce.CustomerName ?? "";
                            row["批次"] = produce.BatchNo ?? "";
                            row["装配线"] = produce.ProduceLine ?? "";
                            row["接收编号"] = produce.SerialNo ?? "";
                            row["远程松闸"] = "";
                            row["盘车齿轮"] = "";
                            row["制动器电压"] = "";
                            row["油漆"] = "";
                            row["主机型号"] = produce.ProduceModel ?? "";

                            // 动态列数据
                            var extwgGroups = produce.SaleBoms
                                .GroupBy(b => b.EWBEZ)
                                .ToDictionary(g => g.Key, g => new
                                {
                                    Matnrs = string.Join("-", g.Select(b => b.IDNRK)),
                                    Summgs = string.Join("-", g.Select(b => b.MENGE))
                                });

                            foreach (var extwg in allExtwgs)
                            {
                                var colName = extwg;
                                var qtyCol = numberMap[extwg];
                                if (extwgGroups.TryGetValue(extwg, out var data))
                                {
                                    row[colName] = data.Matnrs;
                                    row[qtyCol] = data.Summgs;
                                }
                                else
                                {
                                    row[colName] = DBNull.Value;
                                    row[qtyCol] = DBNull.Value;
                                }
                            }

                            dt.Rows.Add(row);
                        }

                        // 创建当前分组的Sheet
                        ISheet sheet = workbook.CreateSheet(produceLine);

                        // 表头行
                        IRow headerRow = sheet.CreateRow(0);
                        for (int i = 0; i < dt.Columns.Count; i++)
                        {
                            headerRow.CreateCell(i).SetCellValue(dt.Columns[i].ColumnName);
                        }

                        // 数据行
                        for (int rowIdx = 0; rowIdx < dt.Rows.Count; rowIdx++)
                        {
                            DataRow dr = dt.Rows[rowIdx];
                            IRow dataRow = sheet.CreateRow(rowIdx + 1);
                            for (int colIdx = 0; colIdx < dt.Columns.Count; colIdx++)
                            {
                                ICell cell = dataRow.CreateCell(colIdx);
                                object value = dr[colIdx];

                                if (value is DBNull)
                                    cell.SetCellValue("");
                                else if (IsNumeric(value))
                                    cell.SetCellValue(Convert.ToDouble(value));
                                else
                                    cell.SetCellValue(value.ToString());
                            }
                        }

                        // 调整列宽
                        for (int i = 0; i < dt.Columns.Count; i++)
                        {
                            sheet.AutoSizeColumn(i);
                            if (sheet.GetColumnWidth(i) < 3000)
                                sheet.SetColumnWidth(i, 3000);
                        }
                    }

                    workbook.Write(memoryStream);
                    excelData = memoryStream.ToArray();
                }

                // 返回文件流（保持不变）
                var response = new HttpResponseMessage(HttpStatusCode.OK)
                {
                    Content = new StreamContent(new MemoryStream(excelData))
                    {
                        Headers =
                        {
                            ContentType = new MediaTypeHeaderValue("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"),
                            ContentDisposition = new ContentDispositionHeaderValue("attachment")
                            {
                                FileName = "配送清单.xlsx"
                            }
                        }
                    }
                };
                return ResponseMessage(response);
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
                return Json(result);
            }
        }

        // 辅助方法：创建日期格式样式
        private ICellStyle CreateDateCellStyle(IWorkbook workbook)
        {
            ICellStyle style = workbook.CreateCellStyle();
            IDataFormat format = workbook.CreateDataFormat();
            style.DataFormat = format.GetFormat("yyyy-mm-dd");
            return style;
        }

        // 辅助方法：判断是否为数值类型
        private bool IsNumeric(object value)
        {
            return value is int || value is double || value is decimal || value is long;
        }

        #endregion

        #region 导出配送清单

        /// <summary>
        /// 导出配送清单
        /// </summary>
        /// <param name="req">排产列表请求参数</param>
        /// <returns></returns>
        [HttpGet]
        public IHttpActionResult ExportDeliveryList([FromUri] Produce_SchedulingListReq req)
        {
            try
            {
                var itemDataList = _app.GetExportInfo(req);

                // 记录日志：查询到的排产数据数量
                LogUtil.WriteLog($"ExportDeliveryList: 查询到排产数据 {itemDataList?.Count ?? 0} 条");

                // 如果没有排产数据，导出空表格
                if (itemDataList == null || itemDataList.Count == 0)
                {
                    LogUtil.WriteLog("ExportDeliveryList: 没有找到排产数据，导出空表格");
                    var emptyData = new List<DeliveryListItem>();
                    var emptyColumns = ExcelService.FetchDefaultColumnList<DeliveryListItem>();
                    string emptyFileName = GetCurrentUser().UserName + "_配送清单_" + DateTime.Now.ToString("yyyyMMddHHmmssfff");
                    return ExportToExcelFile(emptyData, emptyColumns, emptyFileName, "配送清单");
                }

                // 获取SaleBom数据
                string queryMatnrVersionUri = ConfigurationManager.AppSettings["SapUrl"] + ConfigurationManager.AppSettings["GetSaleBom"];
                string token = ConfigurationManager.AppSettings["SapToken"];

                // 记录日志：SAP接口信息
                LogUtil.WriteLog($"ExportDeliveryList: SAP接口地址 {queryMatnrVersionUri}, Token: {(!string.IsNullOrEmpty(token) ? "已配置" : "未配置")}");

                List<SaleBomReq> lineQueries = itemDataList.Select(produce => new SaleBomReq
                {
                    WERKS = "2002",
                    VBELN = produce.SaleSapNo,
                    POSNR = produce.SaleSapLine?.ToString("D6")
                }).ToList().Where(t => !string.IsNullOrEmpty(t.VBELN)).ToList();

                // 记录日志：发送给SAP的查询条件
                LogUtil.WriteLog($"ExportDeliveryList: 发送给SAP的查询条件数量 {lineQueries.Count} 条");
                LogUtil.WriteLog($"ExportDeliveryList: SAP查询请求数据: {JsonConvert.SerializeObject(lineQueries)}");

                // 如果没有有效的SAP查询条件，导出空表格
                if (lineQueries.Count == 0)
                {
                    LogUtil.WriteLog("ExportDeliveryList: 没有有效的SAP查询条件（销售订单号为空），导出空表格");
                    var emptyData = new List<DeliveryListItem>();
                    var emptyColumns = ExcelService.FetchDefaultColumnList<DeliveryListItem>();
                    string emptyFileName = GetCurrentUser().UserName + "_配送清单_" + DateTime.Now.ToString("yyyyMMddHHmmssfff");
                    return ExportToExcelFile(emptyData, emptyColumns, emptyFileName, "配送清单");
                }

                string postRst = HttpUtil.HttpPost($"{queryMatnrVersionUri}?token={token}", JsonConvert.SerializeObject(lineQueries), "POST");

                // 记录日志：SAP接口返回结果
                LogUtil.WriteLog($"ExportDeliveryList: SAP接口返回数据: {postRst}");

                // 如果SAP接口返回空数据，导出空表格
                if (string.IsNullOrEmpty(postRst))
                {
                    LogUtil.WriteLog("ExportDeliveryList: SAP接口返回空数据，导出空表格");
                    var emptyData = new List<DeliveryListItem>();
                    var emptyColumns = ExcelService.FetchDefaultColumnList<DeliveryListItem>();
                    string emptyFileName = GetCurrentUser().UserName + "_配送清单_" + DateTime.Now.ToString("yyyyMMddHHmmssfff");
                    return ExportToExcelFile(emptyData, emptyColumns, emptyFileName, "配送清单");
                }

                List<SaleBomRes> saleBomList;
                try
                {
                    saleBomList = JsonConvert.DeserializeObject<List<SaleBomRes>>(postRst);
                }
                catch (JsonException ex)
                {
                    LogUtil.WriteLog($"ExportDeliveryList: SAP接口返回数据格式错误: {ex.Message}");
                    throw new Exception("SAP接口返回数据格式错误，无法解析BOM数据: " + ex.Message);
                }

                // 如果BOM数据为空，导出空表格
                if (saleBomList == null || saleBomList.Count == 0)
                {
                    LogUtil.WriteLog("ExportDeliveryList: SAP接口返回的BOM数据为空，导出空表格");
                    var emptyData = new List<DeliveryListItem>();
                    var emptyColumns = ExcelService.FetchDefaultColumnList<DeliveryListItem>();
                    string emptyFileName = GetCurrentUser().UserName + "_配送清单_" + DateTime.Now.ToString("yyyyMMddHHmmssfff");
                    return ExportToExcelFile(emptyData, emptyColumns, emptyFileName, "配送清单");
                }

                LogUtil.WriteLog($"ExportDeliveryList: 成功获取BOM数据 {saleBomList.Count} 条");

                // 整理配送清单数据
                var deliveryListData = new List<DeliveryListItem>();
                int sequenceNo = 1;

                foreach (var bomItem in saleBomList)
                {
                    deliveryListData.Add(new DeliveryListItem
                    {
                        序号 = sequenceNo++,
                        物料编码 = bomItem.MATNR ?? "",
                        物料描述 = bomItem.OJTXB ?? "",
                        数量 = bomItem.MENGE ?? "",
                        单位 = bomItem.VRKME ?? "",
                        工作中心编码 = bomItem.IDNRK ?? "",
                        工作中心名称 = bomItem.OJTXP ?? "",
                        站点编码 = bomItem.WERKS ?? "",
                        站点名称 = "西子富沃德", // 固定值，可根据需要调整
                        排产类型 = "正常排产" // 固定值，可根据需要调整
                    });
                }

                LogUtil.WriteLog($"ExportDeliveryList: 整理配送清单数据完成，共 {deliveryListData.Count} 条记录");

                // 使用标准的导出方法
                var columns = ExcelService.FetchDefaultColumnList<DeliveryListItem>();
                string exportFileName = GetCurrentUser().UserName + "_配送清单_" + DateTime.Now.ToString("yyyyMMddHHmmssfff");

                LogUtil.WriteLog("ExportDeliveryList: 配送清单导出成功");
                return ExportToExcelFile(deliveryListData, columns, exportFileName, "配送清单");
            }
            catch (Exception ex)
            {
                LogUtil.WriteLog($"ExportDeliveryList: 导出过程中发生异常: {ex.Message}");
                LogUtil.WriteLog($"ExportDeliveryList: 异常堆栈: {ex.StackTrace}");

                // 重新抛出异常，让全局异常处理器处理
                throw;
            }
        }

        #endregion

        #region 导出更新模板

        /// <summary>
        /// 导出
        /// </summary>
        /// <param name="keyword"></param>
        /// <param name="dateValue"></param>
        /// <returns></returns>
        [HttpPost]
        public IHttpActionResult ExportUpdateTemp([FromBody] string[] ids)
        {
            var result = new ResponseData();
            try
            {
                string modelName = "";
                var itemsDatas = _app.GetList(t => ids.Contains(t.Id)).ToList();
                var columns = ExcelService.FetchDefaultColumnList<Produce_Scheduling>();
                string[] ignoreField = new string[]
                {
                    "DocNum", "DocLine", "OrderType", "SaleSapNo",
                    "SaleSapLine", "CustomerOrderNo", "CustomerCode",
                    "CustomerName", "CustomerAdd", "SettlementAdd",
                    "Quantity", "Unit", "Weight", "PlanAssemblyDate",
                    "PlanStockTime", "ActualStockTime", "ProductionRemark",
                    "Status", "ProduceLine", "SequenceNo", "ExpeditingDate",
                    "SerialNo", "BatchNo", "OrderId", "PcsId",
                    "ProduceSchedulingDate", "PlanStartTime", "PlanEndTime",
                    "DeliveryDate", "ProduceModel", "MUser",
                    "MTime", "DUser", "DTime", "CUser", "CTime", "IsDelete", "Remark",
                    "ItemName", "ItemCode", "CustomerItemCode", "FactoryNo"
                };
                var ignoreFieldList = columns.Where(t => ignoreField.Contains(t.ColumnName)).ToList();
                foreach (var userColumn in ignoreFieldList)
                {
                    columns.Remove(userColumn);
                }

                // 全部导出
                return ExportToExcelFile(itemsDatas, columns);
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
                return Json(result);
            }
        }

        #endregion

        #region 接收PCS订单数据

        /// <summary>
        /// 过账
        /// </summary>
        /// <param name="DocNums">交货单号</param>
        /// <returns></returns>
        [HttpPost]
        [AllowAnonymousAttribute]
        public IHttpActionResult SaveForPcs([FromBody] Produce_SchedulingPcsReq schedulingPcsReq)
        {
            var result = new ResponseData();
            try
            {
                string error_message = "";
                bool postResult = _app.SaveForPcs(schedulingPcsReq, out error_message);
                if (!postResult)
                {
                    result.Code = (int)WMSStatusCode.Failed;
                    result.Message = error_message;
                    result.MessageParam = 2010;
                }
                else
                {
                    result.Code = (int)WMSStatusCode.Success;
                    result.Message = error_message;
                    result.MessageParam = 2000;
                }
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }

            return Json(result);
        }

        #endregion

        #region 更新Pcs状态

        /// <summary>
        /// 更新Pcs状态
        /// </summary>
        /// <param name="req">更新参数</param>
        /// <returns></returns>
        [HttpPost]
        public IHttpActionResult UpdatePcsStatus([FromBody] PcsStatusUpdateReq req)
        {
            var result = new ResponseData();
            try
            {
                string error_message = "";
                bool postResult = _app.UpdatePcsStatus(req, out error_message);
                if (!postResult)
                {
                    result.Code = (int)WMSStatusCode.Failed;
                    result.Message = error_message;
                    result.MessageParam = 2010;
                }
                else
                {
                    result.Code = (int)WMSStatusCode.Success;
                    result.Message = error_message;
                    result.MessageParam = 2000;
                }
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }

            return Json(result);
        }

        #endregion

        #region 私有方法

        /// <summary>
        /// 创建空的配送清单Excel文件
        /// </summary>
        /// <returns></returns>
        private IHttpActionResult CreateEmptyDeliveryListExcel()
        {
            try
            {
                // 创建Excel文件
                byte[] excelData;
                using (var memoryStream = new MemoryStream())
                {
                    IWorkbook workbook = new XSSFWorkbook();
                    ISheet sheet = workbook.CreateSheet("配送清单");
                    sheet.DefaultColumnWidth = 20;

                    // 创建表头
                    var headers = new string[]
                    {
                        "序号", "物料编码", "物料描述", "数量", "单位",
                        "工作中心编码", "工作中心名称", "站点编码", "站点名称", "排产类型"
                    };

                    IRow headerRow = sheet.CreateRow(0);
                    for (int i = 0; i < headers.Length; i++)
                    {
                        headerRow.CreateCell(i).SetCellValue(headers[i]);
                    }

                    // 自动调整列宽
                    for (int i = 0; i < headers.Length; i++)
                    {
                        sheet.AutoSizeColumn(i);
                        if (sheet.GetColumnWidth(i) < 3000)
                        {
                            sheet.SetColumnWidth(i, 3000);
                        }
                    }

                    workbook.Write(memoryStream);
                    excelData = memoryStream.ToArray();
                }

                LogUtil.WriteLog($"ExportDeliveryList: 空配送清单Excel文件创建成功，文件大小 {excelData.Length} 字节");

                // 返回文件流
                var response = new HttpResponseMessage(HttpStatusCode.OK)
                {
                    Content = new StreamContent(new MemoryStream(excelData))
                    {
                        Headers =
                        {
                            ContentType = new MediaTypeHeaderValue("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"),
                            ContentDisposition = new ContentDispositionHeaderValue("attachment")
                            {
                                FileName = "配送清单.xlsx"
                            }
                        }
                    }
                };

                return ResponseMessage(response);
            }
            catch (Exception ex)
            {
                LogUtil.WriteLog($"ExportDeliveryList: 创建空Excel文件失败: {ex.Message}");
                throw new Exception("创建空Excel文件失败: " + ex.Message);
            }
        }

        #endregion
    }
}