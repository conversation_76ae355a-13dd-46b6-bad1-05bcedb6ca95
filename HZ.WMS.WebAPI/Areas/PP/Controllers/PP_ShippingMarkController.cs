using System;
using System.Collections.Generic;
using System.Linq;
using System.Web.Http;
using HZ.Core.Http;
using HZ.Core.Logging;
using HZ.Core.Office;
using HZ.WMS.Application;
using HZ.WMS.Application.MD;
using HZ.WMS.Application.PP;
using HZ.WMS.Entity.PP;
using HZ.WMS.Entity.PP.Dto;
using HZ.WMS.Entity.PP.Import;
using HZ.WMS.WebAPI.Controllers;

namespace HZ.WMS.WebAPI.Areas.PP.Controllers
{
    /// <summary>
    /// 唛头打印
    /// </summary>
    public class PP_ShippingMarkController : ApiBaseController
    {
        private PP_ShippingMarkApp _app = new PP_ShippingMarkApp();
        private MD_ShippingMarkRuleApp _ruleApp = new MD_ShippingMarkRuleApp();
        private MD_BasicSpecificationApp _basicSpecificationApp = new MD_BasicSpecificationApp();
        private MD_NonStandardConfigApp _nonStandardConfigApp = new MD_NonStandardConfigApp();
        private PP_ShippingMarkPrintRecordApp _printRecordApp = new PP_ShippingMarkPrintRecordApp();

        #region 查询分页列表

        /// <summary>
        /// 查询分页列表
        /// </summary>
        /// <param name="page">分页</param>
        /// <param name="req">查询参数</param>
        /// <returns></returns>
        [HttpGet]
        public IHttpActionResult GetPageList([FromUri] Pagination page, [FromUri] PP_ShippingMarkListReq req)
        {
            var result = new ResponseData();
            try
            {
                // 使用关联查询获取包含模板名称的数据
                var itemsData = _app.GetPageListWithTemplateName(page, req);

                result.Data = new ResponsePageData { total = page.Total, items = itemsData };
                result.Code = (int)WMSStatusCode.Success;
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }

            return Json(result);
        }

        #endregion

        #region 获取详情

        /// <summary>
        /// 获取详情
        /// </summary>
        /// <param name="ID">记录ID</param>
        /// <returns></returns>
        [HttpGet]
        public IHttpActionResult GetEntity([FromUri] string ID)
        {
            var result = new ResponseData();
            try
            {
                result.Data = _app.GetEntityByKey(ID);
                result.Code = (int)WMSStatusCode.Success;
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }

            return Json(result);
        }

        #endregion

        #region 添加

        /// <summary>
        /// 添加
        /// </summary>
        /// <param name="entity">实体</param>
        /// <returns></returns>
        [HttpPost]
        public IHttpActionResult Insert([FromBody] PP_ShippingMark entity)
        {
            var result = new ResponseData();
            try
            {
                entity.CUser = GetCurrentUser().LoginAccount;
                entity.CTime = DateTime.Now;
                entity.PrintStatus = false;
                entity.ListPrintStatus = false;

                // 匹配 唛头规则
                var rule = _ruleApp.GetList(t => t.DeliveryCompany == entity.DeliveryCustomer
                    // && t.Brush == entity.BoxBoardBrushing
                    // && t.IsExport == entity.IsExport
                ).ToList().FirstOrDefault();
                
                if (rule != null)
                {
                    entity.PaperType = rule.ShippingMarkPaper;
                    entity.PaperSize = rule.ShippingMarkPaperSize;
                    entity.PrintSystem = rule.ShippingMarkPrintSystem;
                    entity.PrintTemplateId = rule.PrintTemplateId;
                    entity.PrintDirection = rule.PrintDirection;
                }

                var basicSpecification = _basicSpecificationApp.GetList(t => t.Model == entity.ProduceModel).ToList().FirstOrDefault();

                // 空覆盖
                string[] matchTwo = { "RatedLoad", "RatedSpeed", "RatedVoltage", "TractionRatio" };

                // 直接覆盖
                string[] matchOne = { "PackingSize", "PackingNetWeight", "PackingGrossWeight", "RatedPower", "PitchDiameter", "RopeGroove", "GrooveDistance" };
                if (basicSpecification != null)
                {
                    foreach (var item in matchTwo)
                    {
                        if (string.IsNullOrEmpty(entity.GetType().GetProperty(item).GetValue(entity).ToString()))
                        {
                            entity.GetType().GetProperty(item).SetValue(entity, basicSpecification.GetType().GetProperty(item).GetValue(basicSpecification));
                        }
                    }
                    
                    foreach (var item in matchOne)
                    {
                        entity.GetType().GetProperty(item).SetValue(entity, basicSpecification.GetType().GetProperty(item).GetValue(basicSpecification));
                    }
                }

                // 非标处理
                if (!string.IsNullOrEmpty(entity.AcceptNo))
                {
                    string[] matchNonStandard = { "RatedLoad", "RatedSpeed", "RatedVoltage", "TractionRatio", "PackingSize", "PackingNetWeight", "PackingGrossWeight", "RatedPower", "PitchDiameter", "RopeGroove", "GrooveDistance" };
                    var nonStandardConfig = _nonStandardConfigApp.GetList(t => t.AcceptNo == entity.AcceptNo).ToList().FirstOrDefault();

                    if (nonStandardConfig != null)
                    {
                        foreach (var item in matchNonStandard)
                        {
                            if (string.IsNullOrEmpty(entity.GetType().GetProperty(item).GetValue(entity).ToString()))
                            {
                                entity.GetType().GetProperty(item).SetValue(entity, nonStandardConfig.GetType().GetProperty(item).GetValue(nonStandardConfig));
                            }
                        }
                    }
                }

                result.Data = _app.Insert(entity);
                result.Code = (int)WMSStatusCode.Success;
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }

            return Json(result);
        }

        #endregion

        #region 修改

        /// <summary>
        /// 修改
        /// </summary>
        /// <param name="entity">实体</param>
        /// <returns></returns>
        [HttpPost]
        public IHttpActionResult Update([FromBody] PP_ShippingMark entity)
        {
            var result = new ResponseData();
            try
            {
                entity.MUser = GetCurrentUser().LoginAccount;
                entity.MTime = DateTime.Now;

                result.Data = _app.Update(entity);
                result.Code = (int)WMSStatusCode.Success;
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }

            return Json(result);
        }

        #endregion

        #region 删除

        /// <summary>
        /// 删除单条记录
        /// </summary>
        /// <param name="ID">记录ID</param>
        /// <returns></returns>
        [HttpPost]
        public IHttpActionResult DeleteSingle([FromBody] string ID)
        {
            var result = new ResponseData();
            try
            {
                result.Data = _app.DeleteByKey(ID, GetCurrentUser().LoginAccount);
                result.Code = (int)WMSStatusCode.Success;
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }

            return Json(result);
        }

        /// <summary>
        /// 批量删除
        /// </summary>
        /// <param name="ids">记录ID数组</param>
        /// <returns></returns>
        [HttpDelete]
        public IHttpActionResult Delete([FromBody] string[] ids)
        {
            var result = new ResponseData();
            try
            {
                result.Data = _app.DeleteByKeys(ids, GetCurrentUser().LoginAccount);
                result.Code = (int)WMSStatusCode.Success;
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }

            return Json(result);
        }

        #endregion

        #region 导出模板

        /// <summary>
        /// 导出模板
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        public IHttpActionResult ExportToExcelModel()
        {
            var result = new ResponseData();
            try
            {
                var columns = ExcelService.FetchDefaultColumnList<PP_ShippingMark>();
                string modelName = "唛头打印-导入模板";
                //排除不需要的列名称 字符串
                string[] ignoreField = new string[]
                {
                    "Id", "IsDelete", "CUser", "CTime",
                    "MUser", "MTime", "DUser", "DTime"
                };
                var itemsData = new List<PP_ShippingMark>() { };
                var ignoreFieldList = columns.Where(t => ignoreField.Contains(t.ColumnName)).ToList();
                foreach (ExcelColumn<PP_ShippingMark> userColumn in ignoreFieldList)
                {
                    columns.Remove(userColumn);
                }

                return ExportToExcelFile<PP_ShippingMark>(itemsData, columns,
                    GetCurrentUser().UserName + "_" + modelName);
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
                return Json(result);
            }
        }

        /// <summary>
        /// 导出数据
        /// </summary>
        /// <param name="req">查询参数</param>
        /// <returns></returns>
        [HttpGet]
        public IHttpActionResult ExportToExcelFile([FromUri] PP_ShippingMarkListReq req)
        {
            var result = new ResponseData();
            try
            {
                // 使用关联查询获取包含模板名称的数据
                var itemsData = _app.GetListWithTemplateName(req);

                List<ExcelColumn<PP_ShippingMarkWithTemplateName>> columns = ExcelService.FetchDefaultColumnList<PP_ShippingMarkWithTemplateName>();
                string[] ignoreField = new string[] { "IsDelete", "DTime", "DUser", "MUser", "MTime" };

                List<ExcelColumn<PP_ShippingMarkWithTemplateName>> ignoreFieldList = columns.Where(t => ignoreField.Contains(t.ColumnName)).ToList();
                foreach (ExcelColumn<PP_ShippingMarkWithTemplateName> userColumn in ignoreFieldList)
                {
                    columns.Remove(userColumn);
                }

                return ExportToExcelFile<PP_ShippingMarkWithTemplateName>(itemsData, columns);
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
                return Json(result);
            }
        }

        #endregion

        #region 导入

        /// <summary>
        /// 导入
        /// </summary>
        /// <param name="entitys">接收的导入集合</param>
        /// <returns></returns>
        [HttpPost]
        public IHttpActionResult ImportExcelToData([FromBody] List<PP_ShippingMarkImport> entitys)
        {
            var result = new ResponseData();
            try
            {
                // 参数验证
                if (entitys == null)
                {
                    result.Data = false;
                    result.Code = (int)WMSStatusCode.Failed;
                    result.Message = "导入数据不能为空";
                    return Json(result);
                }

                // 获取当前用户
                var currentUser = GetCurrentUser();
                if (currentUser == null)
                {
                    result.Data = false;
                    result.Code = (int)WMSStatusCode.Failed;
                    result.Message = "用户信息获取失败，请重新登录";
                    return Json(result);
                }

                string error_message = "";
                if (_app.ImportExcelToBaseData(entitys, currentUser.LoginAccount, out error_message))
                {
                    result.Data = true;
                    result.Code = (int)WMSStatusCode.Success;
                    result.Message = $"成功导入 {entitys.Count} 条记录";
                }
                else
                {
                    result.Data = false;
                    result.Code = (int)WMSStatusCode.Failed;
                    result.Message = error_message;
                }
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;

                // 构建详细的错误信息
                var errorMessage = ex.Message;
                if (ex.InnerException != null)
                {
                    errorMessage += $" 内部错误: {ex.InnerException.Message}";
                }

                result.Message = $"导入过程中发生异常: {errorMessage}";

                // 记录错误日志
                LogHelper.Instance.LogError($"PP_ShippingMark导入异常: {errorMessage}", ex);

                return Json(result);
            }

            return Json(result);
        }

        #endregion

        #region 打印状态更新

        /// <summary>
        /// 批量更新打印状态
        /// </summary>
        /// <param name="req">批量更新请求参数</param>
        /// <returns></returns>
        [HttpPost]
        public IHttpActionResult BatchUpdatePrintStatus([FromBody]PP_ShippingMarkBatchUpdatePrintStatusReq req)
        {
            var result = new ResponseData();
            try
            {
                if (req == null || req.Ids == null || req.Ids.Count == 0)
                {
                    result.Code = (int)WMSStatusCode.Failed;
                    result.Message = "请求参数不能为空";
                    return Json(result);
                }

                bool success = _app.BatchUpdatePrintStatus(req, GetCurrentUser());
                if (success)
                {
                    result.Code = (int)WMSStatusCode.Success;
                    result.Message = $"批量更新打印状态成功，共更新{req.Ids.Count}条记录";
                }
                else
                {
                    result.Code = (int)WMSStatusCode.Failed;
                    result.Message = "批量更新打印状态失败，未找到对应记录";
                }
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }
            return Json(result);
        }

        #endregion

        #region 打印记录

        /// <summary>
        /// 批量创建打印记录
        /// </summary>
        /// <param name="shippingMarkIds">唛头ID列表</param>
        /// <returns></returns>
        [HttpPost]
        public IHttpActionResult CreatePrintRecords([FromBody] List<string> shippingMarkIds)
        {
            var result = new ResponseData();
            try
            {
                if (shippingMarkIds == null || shippingMarkIds.Count == 0)
                {
                    result.Code = (int)WMSStatusCode.Failed;
                    result.Message = "唛头ID列表不能为空";
                    return Json(result);
                }

                var currentUser = GetCurrentUser();
                if (currentUser == null)
                {
                    result.Code = (int)WMSStatusCode.Failed;
                    result.Message = "用户信息获取失败，请重新登录";
                    return Json(result);
                }

                int recordCount = _printRecordApp.CreatePrintRecords(shippingMarkIds, currentUser.LoginAccount);

                result.Code = (int)WMSStatusCode.Success;
                result.Message = $"成功创建 {recordCount} 条打印记录";
                result.Data = recordCount;
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }

            return Json(result);
        }

        #endregion
    }
}