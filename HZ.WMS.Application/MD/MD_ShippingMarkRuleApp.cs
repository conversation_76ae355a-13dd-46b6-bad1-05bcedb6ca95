using System;
using System.Collections.Generic;
using System.Linq;
using HZ.WMS.Entity.MD;
using HZ.WMS.Entity.MD.Import;
using HZ.WMS.Entity.MD.DTO;
using SqlSugar;
using System.Linq.Expressions;
using HZ.Core.Http;

namespace HZ.WMS.Application.MD
{
    /// <summary>
    /// 唛头规则
    /// </summary>
    public class MD_ShippingMarkRuleApp : BaseApp<MD_ShippingMarkRule>
    {
        private readonly MD_PrintTemplateApp _printTemplateApp = new MD_PrintTemplateApp();

        #region 默认构造函数

        /// <summary>
        /// 默认构造函数
        /// </summary>
        public MD_ShippingMarkRuleApp() : base() { }

        #endregion

        #region 获取包含模板名称的列表

        /// <summary>
        /// 获取包含模板名称的列表
        /// </summary>
        /// <param name="keyword">关键字</param>
        /// <returns></returns>
        public List<MD_ShippingMarkRuleWithTemplateName> GetListWithTemplateName(string keyword)
        {
            var query = DbContext.Queryable<MD_ShippingMarkRule, MD_PrintTemplate>((s, t) => new object[] {
                JoinType.Left, s.PrintTemplateId == t.Id
            })
            .Where((s, t) => !s.IsDelete)
            .WhereIF(!string.IsNullOrEmpty(keyword), (s, t) =>
                s.DeliveryCompany.Contains(keyword) ||
                s.ShippingMarkPrintMethod.Contains(keyword) ||
                s.ShippingMarkEncapsulationMethod.Contains(keyword) ||
                s.ShippingMarkPaper.Contains(keyword) ||
                s.ShippingMarkPaperOption.Contains(keyword) ||
                s.Brush.Contains(keyword) ||
                s.IsExport.Contains(keyword) ||
                s.PrintDirection.Contains(keyword) ||
                s.Remark.Contains(keyword))
            .Select((s, t) => new MD_ShippingMarkRuleWithTemplateName
            {
                Id = s.Id,
                DeliveryCompany = s.DeliveryCompany,
                ShippingMarkPrintMethod = s.ShippingMarkPrintMethod,
                ShippingMarkEncapsulationMethod = s.ShippingMarkEncapsulationMethod,
                ShippingMarkPaper = s.ShippingMarkPaper,
                ShippingMarkPaperOption = s.ShippingMarkPaperOption,
                ShippingMarkPaperSize = s.ShippingMarkPaperSize,
                ShippingMarkPrintSystem = s.ShippingMarkPrintSystem,
                ShippingMarkPackagingMethod = s.ShippingMarkPackagingMethod,
                IsArrivalStationOrPort = s.IsArrivalStationOrPort,
                PrintTemplateId = s.PrintTemplateId,
                TemplateName = t.TemplateName,
                Brush = s.Brush,
                IsExport = s.IsExport,
                PrintDirection = s.PrintDirection,
                Remark = s.Remark,
                ParameterCheckStr = s.ParameterCheckStr,
                IsDelete = s.IsDelete,
                CUser = s.CUser,
                CTime = s.CTime,
                MUser = s.MUser,
                MTime = s.MTime,
                DUser = s.DUser,
                DTime = s.DTime
            })
            .OrderBy(s => s.CTime, OrderByType.Desc);

            return query.ToList();
        }

        /// <summary>
        /// 获取包含模板名称的分页列表
        /// </summary>
        /// <param name="pagination">分页参数</param>
        /// <param name="keyword">关键字</param>
        /// <returns></returns>
        public List<MD_ShippingMarkRuleWithTemplateName> GetPageListWithTemplateName(Pagination pagination, string keyword)
        {
            if (string.IsNullOrEmpty(pagination.Sort))
            {
                pagination.Sort = "s.CTime desc";
            }
            else
            {
                pagination.Sort = pagination.Sort.Replace("ascending", "asc").Replace("descending", "desc");
                // 确保排序字段有表别名
                if (!pagination.Sort.Contains("s.") && !pagination.Sort.Contains("t."))
                {
                    pagination.Sort = "s." + pagination.Sort;
                }
            }

            var query = DbContext.Queryable<MD_ShippingMarkRule, MD_PrintTemplate>((s, t) => new object[] {
                JoinType.Left, s.PrintTemplateId == t.Id
            })
            .Where((s, t) => !s.IsDelete)
            .WhereIF(!string.IsNullOrEmpty(keyword), (s, t) =>
                s.DeliveryCompany.Contains(keyword) ||
                s.ShippingMarkPrintMethod.Contains(keyword) ||
                s.ShippingMarkEncapsulationMethod.Contains(keyword) ||
                s.ShippingMarkPaper.Contains(keyword) ||
                s.ShippingMarkPaperOption.Contains(keyword) ||
                s.Brush.Contains(keyword) ||
                s.IsExport.Contains(keyword) ||
                s.PrintDirection.Contains(keyword) ||
                s.Remark.Contains(keyword))
            .Select((s, t) => new MD_ShippingMarkRuleWithTemplateName
            {
                Id = s.Id,
                DeliveryCompany = s.DeliveryCompany,
                ShippingMarkPrintMethod = s.ShippingMarkPrintMethod,
                ShippingMarkEncapsulationMethod = s.ShippingMarkEncapsulationMethod,
                ShippingMarkPaper = s.ShippingMarkPaper,
                ShippingMarkPaperOption = s.ShippingMarkPaperOption,
                ShippingMarkPaperSize = s.ShippingMarkPaperSize,
                ShippingMarkPrintSystem = s.ShippingMarkPrintSystem,
                ShippingMarkPackagingMethod = s.ShippingMarkPackagingMethod,
                IsArrivalStationOrPort = s.IsArrivalStationOrPort,
                PrintTemplateId = s.PrintTemplateId,
                TemplateName = t.TemplateName,
                Brush = s.Brush,
                IsExport = s.IsExport,
                PrintDirection = s.PrintDirection,
                ParameterCheckStr = s.ParameterCheckStr,
                Remark = s.Remark,
                IsDelete = s.IsDelete,
                CUser = s.CUser,
                CTime = s.CTime,
                MUser = s.MUser,
                MTime = s.MTime,
                DUser = s.DUser,
                DTime = s.DTime
            })
            .OrderBy(pagination.Sort);

            int total = query.Count();
            pagination.Total = total;
            if (pagination.PageSize > total)
            {
                return query.ToPageList(pagination.PageNumber, total);
            }
            return query.ToPageList(pagination.PageNumber, pagination.PageSize);
        }

        #endregion

        #region 导入

        /// <summary>
        /// 导入
        /// </summary>
        /// <param name="excelList">接收的集合</param>
        /// <param name="opUser">当前用户</param>
        /// <param name="error_message">错误消息</param>
        /// <returns></returns>
        public bool ImportExcelToBaseData(List<MD_ShippingMarkRuleImport> excelList, string opUser, out string error_message)
        {
            error_message = "";
            var flag = true;
            DateTime date = DateTime.Now;
            var entityList = new List<MD_ShippingMarkRule>();

            try
            {
                if (excelList == null || excelList.Count == 0)
                {
                    error_message = "导入数据不能为空";
                    return false;
                }

                foreach (var item in excelList)
                {
                    if (string.IsNullOrEmpty(item.DeliveryCompany))
                    {
                        error_message = "发货单位不能为空";
                        return false;
                    }

                    // 验证并获取打印模板ID
                    string printTemplateId = null;
                    if (!string.IsNullOrEmpty(item.TemplateName))
                    {
                        var printTemplate = _printTemplateApp.GetFirstEntity(x => x.TemplateName == item.TemplateName && !x.IsDelete);
                        if (printTemplate == null)
                        {
                            error_message = $"模板名称 '{item.TemplateName}' 不存在";
                            return false;
                        }
                        printTemplateId = printTemplate.Id;
                    }

                    var entity = new MD_ShippingMarkRule
                    {
                        Id = Guid.NewGuid().ToString(),
                        DeliveryCompany = item.DeliveryCompany,
                        ShippingMarkPrintMethod = item.ShippingMarkPrintMethod,
                        ShippingMarkEncapsulationMethod = item.ShippingMarkEncapsulationMethod,
                        ShippingMarkPaper = item.ShippingMarkPaper,
                        ShippingMarkPaperOption = item.ShippingMarkPaperOption,
                        ShippingMarkPaperSize = item.ShippingMarkPaperSize,
                        PrintTemplateId = printTemplateId,
                        Brush = item.Brush,
                        IsExport = item.IsExport,
                        PrintDirection = item.PrintDirection,
                        ParameterCheckStr = item.ParameterCheckStr,
                        Remark = item.Remark,
                        CUser = opUser,
                        CTime = date,
                        IsDelete = false
                    };

                    entityList.Add(entity);
                }

                // 检查是否有重复的发货单位
                var duplicateDeliveryCompanies = entityList.GroupBy(x => x.DeliveryCompany)
                    .Where(g => g.Count() > 1)
                    .Select(g => g.Key)
                    .ToList();

                if (duplicateDeliveryCompanies.Any())
                {
                    error_message = $"导入数据中存在重复的发货单位: {string.Join(", ", duplicateDeliveryCompanies)}";
                    return false;
                }

                // 检查数据库中是否已存在相同的发货单位
                foreach (var entity in entityList)
                {
                    var existingEntity = GetFirstEntity(x => x.DeliveryCompany == entity.DeliveryCompany && !x.IsDelete);
                    if (existingEntity != null)
                    {
                        error_message = $"发货单位 '{entity.DeliveryCompany}' 已存在";
                        return false;
                    }
                }

                // 批量插入数据
                DbContext.Insertable(entityList).ExecuteCommand();
            }
            catch (Exception ex)
            {
                flag = false;
                error_message = ex.Message;
            }

            return flag;
        }

        #endregion

        #region 更新或新增

        /// <summary>
        /// 更新或新增
        /// </summary>
        /// <param name="entity">实体</param>
        /// <param name="user">用户</param>
        /// <returns></returns>
        public bool UpdateOrInsert(MD_ShippingMarkRule entity, string user)
        {
            // 检查是否已存在相同的发货单位（排除当前记录）
            var existingEntity = GetFirstEntity(x => x.DeliveryCompany == entity.DeliveryCompany && x.Id != entity.Id && !x.IsDelete);
            if (existingEntity != null)
            {
                throw new Exception($"发货单位 '{entity.DeliveryCompany}' 已存在");
            }

            if (string.IsNullOrEmpty(entity.Id))
            {
                // 新增
                entity.Id = Guid.NewGuid().ToString();
                entity.CUser = user;
                entity.CTime = DateTime.Now;
                entity.IsDelete = false;
                return (Insert(entity) != null);
            }
            else
            {
                // 更新
                var originalEntity = GetEntityByKey(entity.Id);
                if (originalEntity == null)
                {
                    throw new Exception("记录不存在");
                }

                entity.MUser = user;
                entity.MTime = DateTime.Now;
                return (Update(entity) > 0);
            }
        }

        #endregion
    }
}
