using System;
using System.Collections.Generic;
using System.Linq;
using HZ.WMS.Entity.PP;
using HZ.WMS.Entity.PP.Import;
using HZ.WMS.Entity.PP.Dto;
using HZ.WMS.Entity.MD;
using HZ.WMS.Application.MD;
using SqlSugar;
using System.Reflection;
using System.Linq.Expressions;
using AOS.Core.Utilities;
using HZ.Core.Http;
using HZ.WMS.Entity.Sys;

namespace HZ.WMS.Application.PP
{
    /// <summary>
    /// 唛头打印
    /// </summary>
    public class PP_ShippingMarkApp : BaseApp<PP_ShippingMark>
    {
        private readonly MD_ShippingMarkRuleApp _ruleApp = new MD_ShippingMarkRuleApp();
        private readonly MD_BasicSpecificationApp _basicSpecificationApp = new MD_BasicSpecificationApp();
        private readonly MD_NonStandardConfigApp _nonStandardConfigApp = new MD_NonStandardConfigApp();

        #region 默认构造函数

        /// <summary>
        /// 默认构造函数
        /// </summary>
        public PP_ShippingMarkApp() : base(){}

        #endregion
        
        #region 导入

        /// <summary>
        /// 导入
        /// </summary>
        /// <param name="excelList">接收的集合</param>
        /// <param name="opUser">当前用户</param>
        /// <param name="error_message">错误消息</param>
        /// <returns></returns>
        public bool ImportExcelToBaseData(List<PP_ShippingMarkImport> excelList, string opUser, out string error_message)
        {
            error_message = "";
            var flag = true;
            DateTime date = DateTime.Now;
            var entityList = new List<PP_ShippingMark>();

            try
            {
                // 数据验证
                if (excelList == null || excelList.Count == 0)
                {
                    error_message = "导入数据不能为空";
                    return false;
                }

                // 验证数据并构建实体列表
                for (int i = 0; i < excelList.Count; i++)
                {
                    var item = excelList[i];
                    var rowNumber = i + 1;

                    // 必填字段验证
                    if (string.IsNullOrEmpty(item.OrderNo))
                    {
                        error_message = $"第{rowNumber}行：定单号不能为空";
                        return false;
                    }

                    if (string.IsNullOrEmpty(item.ProduceModel))
                    {
                        error_message = $"第{rowNumber}行：产品型号不能为空";
                        return false;
                    }

                    // 字段长度验证（根据数据库字段长度限制）
                    if (!string.IsNullOrEmpty(item.OrderNo) && item.OrderNo.Length > 50)
                    {
                        error_message = $"第{rowNumber}行：定单号长度不能超过50个字符";
                        return false;
                    }

                    if (!string.IsNullOrEmpty(item.ProduceModel) && item.ProduceModel.Length > 100)
                    {
                        error_message = $"第{rowNumber}行：产品型号长度不能超过100个字符";
                        return false;
                    }

                    if (!string.IsNullOrEmpty(item.ContractNo) && item.ContractNo.Length > 50)
                    {
                        error_message = $"第{rowNumber}行：合同号长度不能超过50个字符";
                        return false;
                    }

                    if (!string.IsNullOrEmpty(item.FactoryNo) && item.FactoryNo.Length > 50)
                    {
                        error_message = $"第{rowNumber}行：出厂编号长度不能超过50个字符";
                        return false;
                    }

                    var entity = new PP_ShippingMark
                    {
                        Id = Guid.NewGuid().ToString(),
                        PrintStatus = ParseBoolFromString(item.ShippingMarkPrintStatus),
                        PrintSystem = item.PrintSystem?.Trim(),
                        PaperSize = item.PaperSize?.Trim(),
                        PaperType = item.PaperType?.Trim(),
                        SapProduceNo = item.SapProduceNo?.Trim(),
                        FactoryNo = item.FactoryNo?.Trim(),
                        OrderNo = item.OrderNo?.Trim(),
                        ContractNo = item.ContractNo?.Trim(),
                        DeliveryCustomer = item.DeliveryCustomer?.Trim(),
                        ProduceModel = item.ProduceModel?.Trim(),
                        ProducePart = item.ProducePart?.Trim(),
                        AssemblyLine = item.AssemblyLine?.Trim(),
                        AssemblyDate = item.AssemblyDate,
                        RatedLoad = item.RatedLoad?.Trim(),
                        RatedSpeed = item.RatedSpeed?.Trim(),
                        RatedVoltage = item.RatedVoltage?.Trim(),
                        NameplateRequirements = item.NameplateRequirements?.Trim(),
                        TractionRatio = item.TractionRatio?.Trim(),
                        CustomerModel = item.CustomerModel?.Trim(),
                        BoxBoardBrushing = item.BoxBoardBrushing?.Trim(),
                        IsExport = item.IsExport,
                        ProjectName = item.ProjectName?.Trim(),
                        AcceptNo = item.AcceptNo?.Trim(),
                        Quantity = ParseIntFromString(item.Quantity),
                        BrakeVoltage = item.BrakeVoltage?.Trim(),
                        SapSalesOrderNo = item.SapSalesOrderNo?.Trim(),
                        SapSalesOrderLineNo = item.SapSalesOrderLineNo?.Trim(),
                        ProductionManager = item.ProductionManager?.Trim(),
                        PackingSize = item.PackingSize?.Trim(),
                        PackingNetWeight = item.PackingNetWeight?.Trim(),
                        PackingGrossWeight = item.PackingGrossWeight?.Trim(),
                        RatedPower = item.Power?.Trim(),
                        PitchDiameter = item.PitchDiameter?.Trim(),
                        RopeGroove = item.RopeGroove?.Trim(),
                        GrooveDistance = item.GrooveDistance?.Trim(),
                        ShippingMarkPrintTime = item.ShippingMarkPrintTime,
                        ListPrintStatus = ParseBoolFromString(item.ListPrintStatus),
                        ListPrintTime = item.ListPrintTime,
                        PrintDirection = item.PrintDirection?.Trim(),
                        Remark = item.Remark?.Trim(),
                        CUser = opUser,
                        CTime = date,
                        IsDelete = false
                    };

                    entityList.Add(entity);
                }

                // 检查是否有重复的出厂编号
                var duplicateFactoryNos = entityList
                    .Where(x => !string.IsNullOrEmpty(x.FactoryNo))
                    .GroupBy(x => x.FactoryNo)
                    .Where(g => g.Count() > 1)
                    .Select(g => g.Key)
                    .ToList();

                if (duplicateFactoryNos.Any())
                {
                    error_message = $"导入数据中存在重复的出厂编号: {string.Join(", ", duplicateFactoryNos)}";
                    return false;
                }

                // 检查数据库中是否已存在相同的出厂编号
                var factoryNos = entityList.Where(x => !string.IsNullOrEmpty(x.FactoryNo)).Select(x => x.FactoryNo).ToList();
                if (factoryNos.Any())
                {
                    var existingFactoryNos = GetList(x => factoryNos.Contains(x.FactoryNo) && !x.IsDelete)
                        .Select(x => x.FactoryNo)
                        .ToList();

                    if (existingFactoryNos.Any())
                    {
                        error_message = $"以下出厂编号已存在: {string.Join(", ", existingFactoryNos)}";
                        return false;
                    }
                }

                if (entityList.Count > 0)
                {
                    // 批量处理业务规则
                    ApplyBusinessRules(entityList);

                    // 批量处理参数检查
                    ApplyParameterCheck(entityList);

                    // 批量插入数据
                    DbContext.Insertable(entityList).ExecuteCommand();
                }

                return flag;
            }
            catch (Exception ex)
            {
                // 记录详细的错误信息
                var innerMessage = ex.InnerException?.Message ?? "";
                error_message = $"导入失败: {ex.Message}";
                if (!string.IsNullOrEmpty(innerMessage))
                {
                    error_message += $" 详细信息: {innerMessage}";
                }

                // 记录到日志
                HZ.Core.Logging.LogHelper.Instance.LogError($"PP_ShippingMark导入失败: {error_message}", ex);

                return false;
            }
        }

        #endregion

        #region 业务规则处理

        /// <summary>
        /// 批量应用业务规则
        /// </summary>
        /// <param name="entityList">实体列表</param>
        private void ApplyBusinessRules(List<PP_ShippingMark> entityList)
        {
            try
            {
                // 1. 批量获取唛头规则
                var deliveryCustomers = entityList.Where(x => !string.IsNullOrEmpty(x.DeliveryCustomer))
                    .Select(x => x.DeliveryCustomer).Distinct().ToList();

                var rules = new Dictionary<string, MD_ShippingMarkRule>();
                if (deliveryCustomers.Any())
                {
                    var ruleList = _ruleApp.GetList(t => deliveryCustomers.Contains(t.DeliveryCompany)).ToList();
                    foreach (var rule in ruleList)
                    {
                        if (!rules.ContainsKey(rule.DeliveryCompany))
                        {
                            rules[rule.DeliveryCompany] = rule;
                        }
                    }
                }

                // 2. 批量获取基础规格
                var produceModels = entityList.Where(x => !string.IsNullOrEmpty(x.ProduceModel))
                    .Select(x => x.ProduceModel).Distinct().ToList();

                var basicSpecifications = new Dictionary<string, MD_BasicSpecification>();
                if (produceModels.Any())
                {
                    var specList = _basicSpecificationApp.GetList(t => produceModels.Contains(t.SAPProductModel)).ToList();
                    foreach (var spec in specList)
                    {
                        if (!basicSpecifications.ContainsKey(spec.SAPProductModel))
                        {
                            basicSpecifications[spec.SAPProductModel] = spec;
                        }
                    }
                }

                // 3. 批量获取非标配置
                var acceptNos = entityList.Where(x => !string.IsNullOrEmpty(x.AcceptNo))
                    .Select(x => x.AcceptNo).Distinct().ToList();

                var nonStandardConfigs = new Dictionary<string, MD_NonStandardConfig>();
                if (acceptNos.Any())
                {
                    var configList = _nonStandardConfigApp.GetList(t => acceptNos.Contains(t.AcceptNo)).ToList();
                    foreach (var config in configList)
                    {
                        if (!nonStandardConfigs.ContainsKey(config.AcceptNo))
                        {
                            nonStandardConfigs[config.AcceptNo] = config;
                        }
                    }
                }

                // 4. 应用业务规则到每个实体
                foreach (var entity in entityList)
                {
                    ApplyBusinessRulesToEntity(entity, rules, basicSpecifications, nonStandardConfigs);
                }
            }
            catch (Exception ex)
            {
                HZ.Core.Logging.LogHelper.Instance.LogError($"应用业务规则失败: {ex.Message}", ex);
                throw;
            }
        }

        /// <summary>
        /// 应用业务规则到单个实体
        /// </summary>
        /// <param name="entity">实体</param>
        /// <param name="rules">唛头规则字典</param>
        /// <param name="basicSpecifications">基础规格字典</param>
        /// <param name="nonStandardConfigs">非标配置字典</param>
        private void ApplyBusinessRulesToEntity(PP_ShippingMark entity,
            Dictionary<string, MD_ShippingMarkRule> rules,
            Dictionary<string, MD_BasicSpecification> basicSpecifications,
            Dictionary<string, MD_NonStandardConfig> nonStandardConfigs)
        {
            // 设置默认值
            entity.PrintStatus = false;
            entity.ListPrintStatus = false;

            // 1. 应用唛头规则
            if (!string.IsNullOrEmpty(entity.DeliveryCustomer) && rules.ContainsKey(entity.DeliveryCustomer))
            {
                var rule = rules[entity.DeliveryCustomer];
                entity.PaperType = rule.ShippingMarkPaper;
                entity.PaperSize = rule.ShippingMarkPaperSize;
                entity.PrintSystem = rule.ShippingMarkPrintSystem;
                entity.PrintTemplateId = rule.PrintTemplateId;
                entity.PrintDirection = rule.PrintDirection;
            }

            // 2. 应用基础规格
            if (!string.IsNullOrEmpty(entity.ProduceModel) && basicSpecifications.ContainsKey(entity.ProduceModel))
            {
                var basicSpec = basicSpecifications[entity.ProduceModel];
                ApplyBasicSpecification(entity, basicSpec);
            }

            // 3. 应用非标配置
            if (!string.IsNullOrEmpty(entity.AcceptNo) && nonStandardConfigs.ContainsKey(entity.AcceptNo))
            {
                var nonStandardConfig = nonStandardConfigs[entity.AcceptNo];
                ApplyNonStandardConfig(entity, nonStandardConfig);
            }
        }

        /// <summary>
        /// 应用基础规格
        /// </summary>
        /// <param name="entity">实体</param>
        /// <param name="basicSpec">基础规格</param>
        private void ApplyBasicSpecification(PP_ShippingMark entity, MD_BasicSpecification basicSpec)
        {
            // 空覆盖字段
            string[] matchTwo = { "RatedLoad", "RatedSpeed", "RatedVoltage", "TractionRatio" };

            // 直接覆盖字段
            string[] matchOne = { "PackingSize", "PackingNetWeight", "PackingGrossWeight", "RatedPower", "PitchDiameter", "RopeGroove", "GrooveDistance" };

            foreach (var fieldName in matchTwo)
            {
                ApplyFieldValue(entity, basicSpec, fieldName, true); // 空覆盖
            }

            foreach (var fieldName in matchOne)
            {
                ApplyFieldValue(entity, basicSpec, fieldName, false); // 直接覆盖
            }
        }

        /// <summary>
        /// 应用非标配置
        /// </summary>
        /// <param name="entity">实体</param>
        /// <param name="nonStandardConfig">非标配置</param>
        private void ApplyNonStandardConfig(PP_ShippingMark entity, MD_NonStandardConfig nonStandardConfig)
        {
            string[] matchNonStandard = { "RatedLoad", "RatedSpeed", "RatedVoltage", "TractionRatio", "PackingSize", "PackingNetWeight", "PackingGrossWeight", "RatedPower", "PitchDiameter", "RopeGroove", "GrooveDistance" };

            foreach (var fieldName in matchNonStandard)
            {
                ApplyFieldValue(entity, nonStandardConfig, fieldName, true); // 空覆盖
            }
        }

        /// <summary>
        /// 应用字段值
        /// </summary>
        /// <param name="entity">目标实体</param>
        /// <param name="source">源对象</param>
        /// <param name="fieldName">字段名</param>
        /// <param name="onlyIfEmpty">是否只在目标字段为空时覆盖</param>
        private void ApplyFieldValue(object entity, object source, string fieldName, bool onlyIfEmpty)
        {
            try
            {
                var entityProperty = entity.GetType().GetProperty(fieldName);
                var sourceProperty = source.GetType().GetProperty(fieldName);

                if (entityProperty != null && sourceProperty != null)
                {
                    var sourceValue = sourceProperty.GetValue(source);

                    if (sourceValue != null)
                    {
                        if (onlyIfEmpty)
                        {
                            var entityValue = entityProperty.GetValue(entity);
                            if (entityValue == null || string.IsNullOrEmpty(entityValue.ToString()))
                            {
                                entityProperty.SetValue(entity, sourceValue);
                            }
                        }
                        else
                        {
                            entityProperty.SetValue(entity, sourceValue);
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                HZ.Core.Logging.LogHelper.Instance.LogError($"应用字段值失败 - 字段: {fieldName}, 错误: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// 批量处理参数检查
        /// </summary>
        /// <param name="entityList">实体列表</param>
        private void ApplyParameterCheck(List<PP_ShippingMark> entityList)
        {
            try
            {
                foreach (var entity in entityList)
                {
                    ApplyParameterCheckToEntity(entity);
                }
            }
            catch (Exception ex)
            {
                HZ.Core.Logging.LogHelper.Instance.LogError($"参数检查失败: {ex.Message}", ex);
                throw;
            }
        }

        /// <summary>
        /// 对单个实体进行参数检查
        /// </summary>
        /// <param name="entity">实体</param>
        private void ApplyParameterCheckToEntity(PP_ShippingMark entity)
        {
            try
            {
                // 根据发货单位获取对应的规则
                if (string.IsNullOrEmpty(entity.DeliveryCustomer))
                {
                    return;
                }

                var rule = _ruleApp.GetFirstEntity(x => x.DeliveryCompany == entity.DeliveryCustomer);
                if (rule == null || string.IsNullOrEmpty(rule.ParameterCheckStr))
                {
                    return;
                }

                // 用逗号分割 ParameterCheckStr 获取字段名称集合
                var fieldNames = rule.ParameterCheckStr.Split(',')
                    .Select(x => x.Trim())
                    .Where(x => !string.IsNullOrEmpty(x))
                    .ToList();

                if (!fieldNames.Any())
                {
                    return;
                }

                // 检查这些字段是否为空
                bool hasEmptyField = false;
                var entityType = entity.GetType();

                foreach (var fieldName in fieldNames)
                {
                    var property = entityType.GetProperty(fieldName);
                    if (property != null)
                    {
                        var value = property.GetValue(entity);
                        if (value == null || string.IsNullOrEmpty(value.ToString()))
                        {
                            hasEmptyField = true;
                            break;
                        }
                    }
                    else
                    {
                        // 如果字段不存在，记录日志但不影响检查结果
                        HZ.Core.Logging.LogHelper.Instance.LogWarn($"参数检查中指定的字段 '{fieldName}' 在实体中不存在");
                    }
                }

                // 如果有字段为空，则将 ParameterCheck 设为 true
                if (hasEmptyField)
                {
                    entity.ParameterCheck = true;
                }
                else
                {
                    // 如果所有字段都不为空，可以选择清空 ParameterCheck 或设为 false
                    // 根据业务需求，这里设为 false 表示检查通过
                    entity.ParameterCheck = false;
                }
            }
            catch (Exception ex)
            {
                HZ.Core.Logging.LogHelper.Instance.LogError($"对实体进行参数检查失败: {ex.Message}", ex);
                // 出现异常时，为安全起见，设置为需要检查
                entity.ParameterCheck = true;
            }
        }

        #endregion

        #region 关联查询方法

        /// <summary>
        /// 获取包含模板名称的分页列表
        /// </summary>
        /// <param name="page">分页参数</param>
        /// <param name="req">查询条件</param>
        /// <returns></returns>
        public List<PP_ShippingMarkWithTemplateName> GetPageListWithTemplateName(Pagination page, PP_ShippingMarkListReq req)
        {
            if (string.IsNullOrEmpty(page.Sort))
            {
                page.Sort = "s.CTime desc";
            }
            else
            {
                page.Sort = page.Sort.Replace("ascending", "asc").Replace("descending", "desc");
                // 确保排序字段有表别名
                if (!page.Sort.Contains("s.") && !page.Sort.Contains("t."))
                {
                    page.Sort = "s." + page.Sort;
                }
            }

            var query = DbContext.Queryable<PP_ShippingMark, MD_PrintTemplate>((s, t) => new object[] {
                JoinType.Left, s.PrintTemplateId == t.Id
            })
            .Where((s, t) => !s.IsDelete)
            .WhereIF(!string.IsNullOrEmpty(req.ContractNo), (s, t) => s.ContractNo.Contains(req.ContractNo))
            .WhereIF(!string.IsNullOrEmpty(req.FactoryNo), (s, t) => s.FactoryNo.Contains(req.FactoryNo))
            .WhereIF(req.AssemblyStartDate.HasValue, (s, t) => s.AssemblyDate >= DateUtil.GetStartTime(req.AssemblyStartDate.Value))
            .WhereIF(req.AssemblyEndDate.HasValue, (s, t) => s.AssemblyDate <= DateUtil.GetEndTime(req.AssemblyEndDate.Value))
            .WhereIF(req.PrintStatus.HasValue, (s, t) => s.PrintStatus == req.PrintStatus.Value)
            .WhereIF(!string.IsNullOrEmpty(req.PrintSystem), (s, t) => s.PrintSystem.Contains(req.PrintSystem))
            .WhereIF(!string.IsNullOrEmpty(req.IsExport), (s, t) => s.IsExport.Contains(req.IsExport))
            .WhereIF(!string.IsNullOrEmpty(req.AssemblyLine), (s, t) => s.AssemblyLine.Contains(req.AssemblyLine))
            .WhereIF(!string.IsNullOrEmpty(req.DeliveryCustomer), (s, t) => s.DeliveryCustomer.Contains(req.DeliveryCustomer))
            .WhereIF(!string.IsNullOrEmpty(req.OrderNo), (s, t) => s.OrderNo.Contains(req.OrderNo))
            .WhereIF(!string.IsNullOrEmpty(req.BoxBoardBrushing), (s, t) => s.BoxBoardBrushing.Contains(req.BoxBoardBrushing))
            .WhereIF(!string.IsNullOrEmpty(req.PrintDirection), (s, t) => s.PrintDirection.Contains(req.PrintDirection))
            .Select((s, t) => new { ShippingMark = s, Template = t })
            .OrderBy(page.Sort);

            int total = query.Count();
            page.Total = total;

            var queryResult = page.PageSize > total
                ? query.ToPageList(page.PageNumber, total)
                : query.ToPageList(page.PageNumber, page.PageSize);

            // 使用BeanUtil.Copy转换结果
            var result = new List<PP_ShippingMarkWithTemplateName>();
            foreach (var item in queryResult)
            {
                var dto = BeanUtil.Copy<PP_ShippingMarkWithTemplateName>(item.ShippingMark);
                dto.TemplateName = item.Template?.TemplateName;
                result.Add(dto);
            }

            return result;
        }

        /// <summary>
        /// 获取包含模板名称的列表（用于导出）
        /// </summary>
        /// <param name="req">查询条件</param>
        /// <returns></returns>
        public List<PP_ShippingMarkWithTemplateName> GetListWithTemplateName(PP_ShippingMarkListReq req)
        {
            var query = DbContext.Queryable<PP_ShippingMark, MD_PrintTemplate>((s, t) => new object[] {
                JoinType.Left, s.PrintTemplateId == t.Id
            })
            .Where((s, t) => !s.IsDelete)
            .WhereIF(!string.IsNullOrEmpty(req.ContractNo), (s, t) => s.ContractNo.Contains(req.ContractNo))
            .WhereIF(!string.IsNullOrEmpty(req.FactoryNo), (s, t) => s.FactoryNo.Contains(req.FactoryNo))
            .WhereIF(req.AssemblyStartDate.HasValue, (s, t) => s.AssemblyDate >= req.AssemblyStartDate.Value)
            .WhereIF(req.AssemblyEndDate.HasValue, (s, t) => s.AssemblyDate <= req.AssemblyEndDate.Value)
            .WhereIF(req.PrintStatus.HasValue, (s, t) => s.PrintStatus == req.PrintStatus.Value)
            .WhereIF(!string.IsNullOrEmpty(req.PrintSystem), (s, t) => s.PrintSystem.Contains(req.PrintSystem))
            .WhereIF(!string.IsNullOrEmpty(req.IsExport), (s, t) => s.IsExport.Contains(req.IsExport))
            .WhereIF(!string.IsNullOrEmpty(req.AssemblyLine), (s, t) => s.AssemblyLine.Contains(req.AssemblyLine))
            .WhereIF(!string.IsNullOrEmpty(req.DeliveryCustomer), (s, t) => s.DeliveryCustomer.Contains(req.DeliveryCustomer))
            .WhereIF(!string.IsNullOrEmpty(req.OrderNo), (s, t) => s.OrderNo.Contains(req.OrderNo))
            .WhereIF(!string.IsNullOrEmpty(req.BoxBoardBrushing), (s, t) => s.BoxBoardBrushing.Contains(req.BoxBoardBrushing))
            .Select((s, t) => new { ShippingMark = s, Template = t })
            .OrderBy("ShippingMark.CTime desc");

            var queryResult = query.ToList();

            // 使用BeanUtil.Copy转换结果
            var result = new List<PP_ShippingMarkWithTemplateName>();
            foreach (var item in queryResult)
            {
                var dto = BeanUtil.Copy<PP_ShippingMarkWithTemplateName>(item.ShippingMark);
                dto.TemplateName = item.Template?.TemplateName;
                result.Add(dto);
            }

            return result;
        }

        #endregion

        #region 私有辅助方法

        /// <summary>
        /// 将字符串转换为布尔值
        /// </summary>
        /// <param name="value">字符串值</param>
        /// <returns>布尔值或null</returns>
        private bool? ParseBoolFromString(string value)
        {
            if (string.IsNullOrWhiteSpace(value))
                return null;

            value = value.Trim().ToLower();

            // 支持多种格式
            if (value == "true" || value == "是" || value == "1" || value == "yes" || value == "y")
                return true;

            if (value == "false" || value == "否" || value == "0" || value == "no" || value == "n")
                return false;

            return null;
        }

        /// <summary>
        /// 将字符串转换为整数
        /// </summary>
        /// <param name="value">字符串值</param>
        /// <returns>整数值或null</returns>
        private int? ParseIntFromString(string value)
        {
            if (string.IsNullOrWhiteSpace(value))
                return null;

            if (int.TryParse(value.Trim(), out int result))
                return result;

            return null;
        }

        #endregion

        #region 打印状态更新

        /// <summary>
        /// 批量更新打印状态
        /// </summary>
        /// <param name="req">批量更新请求参数</param>
        /// <param name="currentUser">当前用户</param>
        /// <returns></returns>
        public bool BatchUpdatePrintStatus(PP_ShippingMarkBatchUpdatePrintStatusReq req, Sys_User currentUser)
        {
            try
            {
                if (req == null || req.Ids == null || req.Ids.Count == 0)
                {
                    throw new Exception("请求参数不能为空");
                }

                var entities = GetList(x => req.Ids.Contains(x.Id)).ToList();
                if (entities.Count == 0)
                {
                    return false;
                }

                var updateTime = req.PrintTime ?? (req.PrintStatus ? DateTime.Now : (DateTime?)null);

                foreach (var entity in entities)
                {
                    entity.PrintStatus = req.PrintStatus;
                    if (updateTime.HasValue)
                    {
                        entity.ShippingMarkPrintTime = updateTime.Value;
                    }
                    entity.MTime = DateTime.Now;
                    entity.MUser = currentUser?.LoginAccount ?? "System";
                }

                return Update(entities) > 0;
            }
            catch (Exception ex)
            {
                throw new Exception($"批量更新打印状态失败：{ex.Message}");
            }
        }

        #endregion
    }
}
