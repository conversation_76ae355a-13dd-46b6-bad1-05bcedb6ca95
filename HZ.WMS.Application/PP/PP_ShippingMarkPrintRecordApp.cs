using HZ.WMS.Entity.PP;
using HZ.WMS.Entity.MD;
using HZ.WMS.Application.MD;
using System;
using System.Collections.Generic;
using System.Linq;

namespace HZ.WMS.Application.PP
{
    /// <summary>
    /// 唛头打印记录
    /// </summary>
    public class PP_ShippingMarkPrintRecordApp : BaseApp<PP_ShippingMarkPrintRecord>
    {
        #region 默认构造函数

        /// <summary>
        /// 默认构造函数
        /// </summary>
        public PP_ShippingMarkPrintRecordApp() : base() { }

        #endregion

        #region 批量创建打印记录

        /// <summary>
        /// 批量创建打印记录
        /// </summary>
        /// <param name="shippingMarkIds">唛头ID列表</param>
        /// <param name="currentUser">当前用户</param>
        /// <returns>创建的记录数量</returns>
        public int CreatePrintRecords(List<string> shippingMarkIds, string currentUser)
        {
            if (shippingMarkIds == null || !shippingMarkIds.Any())
            {
                throw new ArgumentException("唛头ID列表不能为空");
            }

            var shippingMarkApp = new PP_ShippingMarkApp();
            var printRecords = new List<PP_ShippingMarkPrintRecord>();

            foreach (var shippingMarkId in shippingMarkIds)
            {
                // 获取唛头信息
                var shippingMark = shippingMarkApp.GetFirstEntity(x => x.Id == shippingMarkId && !x.IsDelete);
                if (shippingMark == null)
                {
                    throw new Exception($"唛头ID {shippingMarkId} 不存在或已删除");
                }

                // 创建打印记录
                var printRecord = new PP_ShippingMarkPrintRecord
                {
                    Id = Guid.NewGuid().ToString(),
                    ShippingMarkId = shippingMarkId,
                    TemplateName = GetTemplateName(shippingMark.PrintTemplateId),
                    PrintDirection = shippingMark.PrintDirection,
                    PaperSize = shippingMark.PaperSize,
                    PaperType = shippingMark.PaperType,
                    CUser = currentUser,
                    CTime = DateTime.Now,
                    IsDelete = false
                };

                printRecords.Add(printRecord);
            }

            // 批量插入
            if (printRecords.Any())
            {
                DbContext.Insertable(printRecords).ExecuteCommand();
            }

            return printRecords.Count;
        }

        #endregion

        #region 私有方法

        /// <summary>
        /// 获取模板名称
        /// </summary>
        /// <param name="templateId">模板ID</param>
        /// <returns>模板名称</returns>
        private string GetTemplateName(string templateId)
        {
            if (string.IsNullOrEmpty(templateId))
            {
                return string.Empty;
            }

            try
            {
                var templateApp = new MD_PrintTemplateApp();
                var template = templateApp.GetFirstEntity(x => x.Id == templateId && !x.IsDelete);
                return template?.TemplateName ?? string.Empty;
            }
            catch
            {
                return string.Empty;
            }
        }

        #endregion
    }
}
