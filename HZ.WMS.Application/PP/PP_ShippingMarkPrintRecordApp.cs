using HZ.WMS.Entity.PP;
using HZ.WMS.Entity.PP.Dto;
using HZ.WMS.Entity.MD;
using HZ.WMS.Application.MD;
using HZ.Core.Http;
using SqlSugar;
using System;
using System.Collections.Generic;
using System.Linq;

namespace HZ.WMS.Application.PP
{
    /// <summary>
    /// 唛头打印记录
    /// </summary>
    public class PP_ShippingMarkPrintRecordApp : BaseApp<PP_ShippingMarkPrintRecord>
    {
        #region 默认构造函数

        /// <summary>
        /// 默认构造函数
        /// </summary>
        public PP_ShippingMarkPrintRecordApp() : base() { }

        #endregion

        #region 批量创建打印记录

        /// <summary>
        /// 批量创建打印记录
        /// </summary>
        /// <param name="shippingMarkIds">唛头ID列表</param>
        /// <param name="currentUser">当前用户</param>
        /// <returns>创建的记录数量</returns>
        public int CreatePrintRecords(List<string> shippingMarkIds, string currentUser)
        {
            if (shippingMarkIds == null || !shippingMarkIds.Any())
            {
                throw new ArgumentException("唛头ID列表不能为空");
            }

            var shippingMarkApp = new PP_ShippingMarkApp();
            var printRecords = new List<PP_ShippingMarkPrintRecord>();
            var currentTime = DateTime.Now;

            // 批量获取唛头信息，提高效率
            var shippingMarks = shippingMarkApp.GetList(x => shippingMarkIds.Contains(x.Id) && !x.IsDelete).ToList();

            // 检查是否所有唛头都存在
            var foundIds = shippingMarks.Select(x => x.Id).ToList();
            var missingIds = shippingMarkIds.Except(foundIds).ToList();
            if (missingIds.Any())
            {
                throw new Exception($"以下唛头ID不存在或已删除: {string.Join(", ", missingIds)}");
            }

            // 批量创建打印记录
            foreach (var shippingMark in shippingMarks)
            {
                var printRecord = new PP_ShippingMarkPrintRecord
                {
                    Id = Guid.NewGuid().ToString(),
                    ShippingMarkId = shippingMark.Id,
                    TemplateName = GetTemplateName(shippingMark.PrintTemplateId),
                    PrintDirection = shippingMark.PrintDirection,
                    PaperSize = shippingMark.PaperSize,
                    PaperType = shippingMark.PaperType,
                    CUser = currentUser,
                    CTime = currentTime,
                    IsDelete = false
                };

                printRecords.Add(printRecord);
            }

            // 使用事务确保数据一致性
            DbContext.Ado.BeginTran();
            try
            {
                // 1. 批量插入打印记录
                if (printRecords.Any())
                {
                    DbContext.Insertable(printRecords).ExecuteCommand();
                }

                // 2. 批量更新唛头打印状态
                var updateSql = @"
                    UPDATE PP_ShippingMark
                    SET PrintStatus = 1,
                        ShippingMarkPrintTime = @PrintTime,
                        MTime = @MTime,
                        MUser = @MUser
                    WHERE Id IN ({0}) AND IsDelete = 0";

                var idParams = string.Join(",", shippingMarkIds.Select((id, index) => $"@Id{index}"));
                var finalSql = string.Format(updateSql, idParams);

                var parameters = new Dictionary<string, object>
                {
                    ["PrintTime"] = currentTime,
                    ["MTime"] = currentTime,
                    ["MUser"] = currentUser
                };

                for (int i = 0; i < shippingMarkIds.Count; i++)
                {
                    parameters[$"Id{i}"] = shippingMarkIds[i];
                }

                DbContext.Ado.ExecuteCommand(finalSql, parameters);

                DbContext.Ado.CommitTran();
            }
            catch (Exception)
            {
                DbContext.Ado.RollbackTran();
                throw;
            }

            return printRecords.Count;
        }

        #endregion

        #region 查询方法

        /// <summary>
        /// 获取包含唛头详细信息的分页列表
        /// </summary>
        /// <param name="page">分页参数</param>
        /// <param name="req">查询条件</param>
        /// <returns></returns>
        public List<PP_ShippingMarkPrintRecordWithDetails> GetPageListWithDetails(Pagination page, PP_ShippingMarkPrintRecordListReq req)
        {
            if (string.IsNullOrEmpty(page.Sort))
            {
                page.Sort = "pr.CTime desc";
            }
            else
            {
                page.Sort = page.Sort.Replace("ascending", "asc").Replace("descending", "desc");
                // 确保排序字段有表别名
                if (!page.Sort.Contains("pr.") && !page.Sort.Contains("sm."))
                {
                    page.Sort = "pr." + page.Sort;
                }
            }

            var query = DbContext.Queryable<PP_ShippingMarkPrintRecord, PP_ShippingMark>((pr, sm) => new object[] {
                JoinType.Left, pr.ShippingMarkId == sm.Id
            })
            .Where((pr, sm) => !pr.IsDelete)
            .WhereIF(!string.IsNullOrEmpty(req.ShippingMarkId), (pr, sm) => pr.ShippingMarkId == req.ShippingMarkId)
            .WhereIF(!string.IsNullOrEmpty(req.TemplateName), (pr, sm) => pr.TemplateName.Contains(req.TemplateName))
            .WhereIF(!string.IsNullOrEmpty(req.PrintDirection), (pr, sm) => pr.PrintDirection == req.PrintDirection)
            .WhereIF(!string.IsNullOrEmpty(req.PaperSize), (pr, sm) => pr.PaperSize == req.PaperSize)
            .WhereIF(!string.IsNullOrEmpty(req.PaperType), (pr, sm) => pr.PaperType == req.PaperType)
            .WhereIF(!string.IsNullOrEmpty(req.CUser), (pr, sm) => pr.CUser.Contains(req.CUser))
            .WhereIF(req.CreateStartTime != null, (pr, sm) => pr.CTime >= req.CreateStartTime)
            .WhereIF(req.CreateEndTime != null, (pr, sm) => pr.CTime <= req.CreateEndTime)
            .WhereIF(!string.IsNullOrEmpty(req.Keyword), (pr, sm) =>
                pr.ShippingMarkId.Contains(req.Keyword) ||
                pr.TemplateName.Contains(req.Keyword) ||
                pr.CUser.Contains(req.Keyword) ||
                sm.FactoryNo.Contains(req.Keyword) ||
                sm.ContractNo.Contains(req.Keyword) ||
                sm.OrderNo.Contains(req.Keyword))
            .Select((pr, sm) => new PP_ShippingMarkPrintRecordWithDetails
            {
                Id = pr.Id,
                ShippingMarkId = pr.ShippingMarkId,
                TemplateName = pr.TemplateName,
                PrintDirection = pr.PrintDirection,
                PaperSize = pr.PaperSize,
                PaperType = pr.PaperType,
                Remark = pr.Remark,
                CUser = pr.CUser,
                CTime = pr.CTime,
                MUser = pr.MUser,
                MTime = pr.MTime,
                // 唛头信息
                FactoryNo = sm.FactoryNo,
                ContractNo = sm.ContractNo,
                OrderNo = sm.OrderNo,
                DeliveryCustomer = sm.DeliveryCustomer,
                ProduceModel = sm.ProduceModel,
                AssemblyLine = sm.AssemblyLine,
                AssemblyDate = sm.AssemblyDate,
                SapProduceNo = sm.SapProduceNo
            })
            .OrderBy(page.Sort);

            int total = query.Count();
            page.Total = total;
            if (page.PageSize > total)
            {
                return query.ToPageList(page.PageNumber, total);
            }
            return query.ToPageList(page.PageNumber, page.PageSize);
        }

        /// <summary>
        /// 获取包含唛头详细信息的列表（不分页）
        /// </summary>
        /// <param name="req">查询条件</param>
        /// <returns></returns>
        public List<PP_ShippingMarkPrintRecordWithDetails> GetListWithDetails(PP_ShippingMarkPrintRecordListReq req)
        {
            var query = DbContext.Queryable<PP_ShippingMarkPrintRecord, PP_ShippingMark>((pr, sm) => new object[] {
                JoinType.Left, pr.ShippingMarkId == sm.Id
            })
            .Where((pr, sm) => !pr.IsDelete)
            .WhereIF(!string.IsNullOrEmpty(req.ShippingMarkId), (pr, sm) => pr.ShippingMarkId == req.ShippingMarkId)
            .WhereIF(!string.IsNullOrEmpty(req.TemplateName), (pr, sm) => pr.TemplateName.Contains(req.TemplateName))
            .WhereIF(!string.IsNullOrEmpty(req.PrintDirection), (pr, sm) => pr.PrintDirection == req.PrintDirection)
            .WhereIF(!string.IsNullOrEmpty(req.PaperSize), (pr, sm) => pr.PaperSize == req.PaperSize)
            .WhereIF(!string.IsNullOrEmpty(req.PaperType), (pr, sm) => pr.PaperType == req.PaperType)
            .WhereIF(!string.IsNullOrEmpty(req.CUser), (pr, sm) => pr.CUser.Contains(req.CUser))
            .WhereIF(req.CreateStartTime != null, (pr, sm) => pr.CTime >= req.CreateStartTime)
            .WhereIF(req.CreateEndTime != null, (pr, sm) => pr.CTime <= req.CreateEndTime)
            .WhereIF(!string.IsNullOrEmpty(req.Keyword), (pr, sm) =>
                pr.ShippingMarkId.Contains(req.Keyword) ||
                pr.TemplateName.Contains(req.Keyword) ||
                pr.CUser.Contains(req.Keyword) ||
                sm.FactoryNo.Contains(req.Keyword) ||
                sm.ContractNo.Contains(req.Keyword) ||
                sm.OrderNo.Contains(req.Keyword))
            .Select((pr, sm) => new PP_ShippingMarkPrintRecordWithDetails
            {
                Id = pr.Id,
                ShippingMarkId = pr.ShippingMarkId,
                TemplateName = pr.TemplateName,
                PrintDirection = pr.PrintDirection,
                PaperSize = pr.PaperSize,
                PaperType = pr.PaperType,
                Remark = pr.Remark,
                CUser = pr.CUser,
                CTime = pr.CTime,
                MUser = pr.MUser,
                MTime = pr.MTime,
                // 唛头信息
                FactoryNo = sm.FactoryNo,
                ContractNo = sm.ContractNo,
                OrderNo = sm.OrderNo,
                DeliveryCustomer = sm.DeliveryCustomer,
                ProduceModel = sm.ProduceModel,
                AssemblyLine = sm.AssemblyLine,
                AssemblyDate = sm.AssemblyDate,
                SapProduceNo = sm.SapProduceNo
            })
            .OrderBy("pr.CTime desc");

            return query.ToList();
        }

        #endregion

        #region 私有方法

        /// <summary>
        /// 获取模板名称
        /// </summary>
        /// <param name="templateId">模板ID</param>
        /// <returns>模板名称</returns>
        private string GetTemplateName(string templateId)
        {
            if (string.IsNullOrEmpty(templateId))
            {
                return string.Empty;
            }

            try
            {
                var templateApp = new MD_PrintTemplateApp();
                var template = templateApp.GetFirstEntity(x => x.Id == templateId && !x.IsDelete);
                return template?.TemplateName ?? string.Empty;
            }
            catch
            {
                return string.Empty;
            }
        }

        #endregion
    }
}
